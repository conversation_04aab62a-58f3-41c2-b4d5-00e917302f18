{"version": 3, "file": "u-checkbox-group.js", "sources": ["uni_modules/uview-plus/components/u-checkbox-group/u-checkbox-group.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1jaGVja2JveC1ncm91cC91LWNoZWNrYm94LWdyb3VwLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-checkbox-group\"\n\t    :class=\"bemClass\"\n\t>\n\t\t<slot></slot>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\t/**\n\t * checkboxGroup 复选框组\n\t * @description 复选框组件一般用于需要多个选择的场景，该组件功能完整，使用方便\n\t * @tutorial https://ijry.github.io/uview-plus/components/checkbox.html\n\t * @property {String}\t\t\tname\t\t\t标识符 \n\t * @property {Array}\t\t\tvalue\t\t\t绑定的值\n\t * @property {String}\t\t\tshape\t\t\t形状，circle-圆形，square-方形 （默认 'square' ）\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用全部checkbox （默认 false ）\n\t * @property {String}\t\t\tactiveColor\t\t选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值 （默认 '#2979ff' ）\n\t * @property {String}\t\t\tinactiveColor\t未选中的颜色 （默认 '#c8c9cc' ）\n\t * @property {String | Number}\tsize\t\t\t整个组件的尺寸 单位px （默认 18 ）\n\t * @property {String}\t\t\tplacement\t\t布局方式，row-横向，column-纵向 （默认 'row' ）\n\t * @property {String | Number}\tlabelSize\t\tlabel的字体大小，px单位  （默认 14 ）\n\t * @property {String}\t\t\tlabelColor\t\tlabel的字体颜色 （默认 '#303133' ）\n\t * @property {Boolean}\t\t\tlabelDisabled\t是否禁止点击文本操作 (默认 false )\n\t * @property {String}\t\t\ticonColor\t\t图标颜色 （默认 '#ffffff' ）\n\t * @property {String | Number}\ticonSize\t\t图标的大小，单位px （默认 12 ）\n\t * @property {String}\t\t\ticonPlacement\t勾选图标的对齐方式，left-左边，right-右边  （默认 'left' ）\n\t * @property {Boolean}\t\t\tborderBottom\tplacement为row时，是否显示下边框 （默认 false ）\n\t * @event {Function}\tchange\t任一个checkbox状态发生变化时触发，回调为一个对象\n\t * @event {Function}\tinput\t修改通过v-model绑定的值时触发，回调为一个对象\n\t * @example <u-checkbox-group></u-checkbox-group>\n\t */\n\texport default {\n\t\tname: 'u-checkbox-group',\n\t\tmixins: [mpMixin, mixin,props],\n\t\tcomputed: {\n\t\t\t// 这里computed的变量，都是子组件u-checkbox需要用到的，由于头条小程序的兼容性差异，子组件无法实时监听父组件参数的变化\n\t\t\t// 所以需要手动通知子组件，这里返回一个parentData变量，供watch监听，在其中去通知每一个子组件重新从父组件(u-checkbox-group)\n\t\t\t// 拉取父组件新的变化后的参数\n\t\t\tparentData() {\n\t\t\t  return [\n\t\t\t\t// #ifdef VUE2\n\t\t\t\tthis.value,\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef VUE3\n\t\t\t\tthis.modelValue,\n\t\t\t\t// #endif\n\t\t\t\tthis.disabled,\n\t\t\t\tthis.inactiveColor,\n\t\t\t\tthis.activeColor,\n\t\t\t\tthis.size,\n\t\t\t\tthis.labelDisabled,\n\t\t\t\tthis.shape,\n\t\t\t\tthis.iconSize,\n\t\t\t\tthis.borderBottom,\n\t\t\t\tthis.placement,\n\t\t\t  ];\n\t\t\t},\n\t\t\tbemClass() {\n\t\t\t\t// this.bem为一个computed变量，在mixin中\n\t\t\t\treturn this.bem('checkbox-group', ['placement'])\n\t\t\t},\n\t\t},\n\t\twatch: {\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\n\t\t\tparentData: {\n\t\t\t  handler() {\n\t\t\t\tif (this.children.length) {\n\t\t\t\t  this.children.map((child) => {\n\t\t\t\t\t// 判断子组件(u-checkbox)如果有init方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\n\t\t\t\t\ttypeof child.init === \"function\" && child.init();\n\t\t\t\t  });\n\t\t\t\t}\n\t\t\t  },\n\t\t\t  deep: true,\n\t\t\t},\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t},\n\t\t// #ifdef VUE3\n\t\temits: ['update:modelValue', 'change'],\n\t\t// #endif\n\t\tmethods: {\n\t\t\t// 将其他的checkbox设置为未选中的状态\n\t\t\tunCheckedOther(childInstance) {\n\t\t\t\tconst values = []\n\t\t\t\tthis.children.map(child => {\n\t\t\t\t\t// 将被选中的checkbox，放到数组中返回\n\t\t\t\t\tif (child.isChecked) {\n\t\t\t\t\t\tvalues.push(child.name)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\n\t\t\t\t// 修改通过v-model绑定的值\n\t\t\t\t// #ifdef VUE3\n\t\t\t\tthis.$emit(\"update:modelValue\", values);\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef VUE2\n\t\t\t\tthis.$emit(\"input\", values);\n\t\t\t\t// #endif\n        // 放在最后更新，否则change事件传出去的values不会更新\n\t\t\t\tthis.$emit('change', values)\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\n\t.u-checkbox-group {\n\n\t\t&--row {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tdisplay: flex;\n\t\t\t/* #endif */\n\t\t\tflex-flow: row wrap;\n\t\t}\n\n\t\t&--column {\n\t\t\t@include flex(column);\n\t\t}\n\t}\n</style>\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-checkbox-group/u-checkbox-group.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props"], "mappings": ";;;;;AAoCC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAACC,2DAAK;AAAA,EAC7B,UAAU;AAAA;AAAA;AAAA;AAAA,IAIT,aAAa;AACX,aAAO;AAAA,QAKR,KAAK;AAAA,QAEL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA;IAEL;AAAA,IACD,WAAW;AAEV,aAAO,KAAK,IAAI,kBAAkB,CAAC,WAAW,CAAC;AAAA,IAC/C;AAAA,EACD;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,YAAY;AAAA,MACV,UAAU;AACX,YAAI,KAAK,SAAS,QAAQ;AACxB,eAAK,SAAS,IAAI,CAAC,UAAU;AAE9B,mBAAO,MAAM,SAAS,cAAc,MAAM,KAAI;AAAA,UAC7C,CAAC;AAAA,QACH;AAAA,MACE;AAAA,MACD,MAAM;AAAA,IACP;AAAA,EACD;AAAA,EACD,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,WAAW,CAAC;AAAA,EACjB;AAAA,EAED,OAAO,CAAC,qBAAqB,QAAQ;AAAA,EAErC,SAAS;AAAA;AAAA,IAER,eAAe,eAAe;AAC7B,YAAM,SAAS,CAAC;AAChB,WAAK,SAAS,IAAI,WAAS;AAE1B,YAAI,MAAM,WAAW;AACpB,iBAAO,KAAK,MAAM,IAAI;AAAA,QACvB;AAAA,OACA;AAID,WAAK,MAAM,qBAAqB,MAAM;AAMtC,WAAK,MAAM,UAAU,MAAM;AAAA,IAC3B;AAAA,EACF;AACD;;;;;;;ACjHD,GAAG,gBAAgB,SAAS;"}