"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_study = require("../../src/api/modules/study.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "study",
  setup(__props) {
    src_stores_modules_user.useUserStore();
    const todayPracticeCount = common_vendor.ref(0);
    const todayCorrectRate = common_vendor.ref(0);
    const todayStudyTime = common_vendor.ref(0);
    const remainingPracticeCount = common_vendor.ref(3);
    common_vendor.onMounted(() => {
      loadStudyStats();
    });
    async function loadStudyStats() {
      try {
        const stats = await src_api_modules_study.getPracticeStats();
        todayPracticeCount.value = stats.todayPracticeCount || 0;
        todayCorrectRate.value = stats.todayCorrectRate || 0;
        todayStudyTime.value = stats.todayStudyTime || 0;
        remainingPracticeCount.value = stats.remainingPracticeCount || 3;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/study/study.vue:81", "加载学习统计失败:", error);
      }
    }
    function getPracticeStatus() {
      if (remainingPracticeCount.value > 0) {
        return `今日还可练习 ${remainingPracticeCount.value} 组`;
      }
      return "今日练习次数已用完";
    }
    function handleTextbookClick() {
      common_vendor.index.showToast({
        title: "功能建设中，敬请期待",
        icon: "none"
      });
    }
    function goToQuestionBank() {
      common_vendor.index.navigateTo({ url: "/pages/study/question-bank" });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(handleTextbookClick),
        b: common_vendor.t(getPracticeStatus()),
        c: common_vendor.o(goToQuestionBank),
        d: common_vendor.t(todayPracticeCount.value),
        e: common_vendor.t(todayCorrectRate.value),
        f: common_vendor.t(todayStudyTime.value)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f273c1e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/study.js.map
