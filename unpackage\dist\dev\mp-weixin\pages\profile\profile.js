"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "profile",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    function getStatusText() {
      switch (userStore.userStatus) {
        case "approved":
          return "已认证";
        case "pending":
          return "审核中";
        case "rejected":
          return "审核未通过";
        case "incomplete":
          return "未完善资料";
        default:
          return "未知状态";
      }
    }
    function getPersonalInfoTitle() {
      if (userStore.isIncomplete) {
        return "完善个人资料";
      } else if (userStore.isRejected) {
        return "修改个人资料";
      } else {
        return "个人信息";
      }
    }
    function goToPersonalInfo() {
      if (userStore.isApproved) {
        common_vendor.index.navigateTo({ url: "/pages/profile/personal-info" });
      } else {
        common_vendor.index.navigateTo({ url: "/pages/register/register" });
      }
    }
    function goToCertificates() {
      common_vendor.index.navigateTo({ url: "/pages/profile/certificates" });
    }
    function goToFeedback() {
      common_vendor.index.navigateTo({ url: "/pages/profile/feedback" });
    }
    function goToAbout() {
      common_vendor.index.navigateTo({ url: "/pages/profile/about" });
    }
    function handleLogout() {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            userStore.clearProfile();
            common_vendor.index.reLaunch({ url: "/pages/login/login" });
          }
        }
      });
    }
    return (_ctx, _cache) => {
      var _a, _b;
      return common_vendor.e({
        a: ((_a = common_vendor.unref(userStore).profile) == null ? void 0 : _a.avatar) || "/static/default-avatar.png",
        b: common_vendor.t(((_b = common_vendor.unref(userStore).profile) == null ? void 0 : _b.nickname) || "未设置昵称"),
        c: common_vendor.t(getStatusText()),
        d: common_vendor.n(common_vendor.unref(userStore).userStatus),
        e: common_vendor.t(getPersonalInfoTitle()),
        f: common_vendor.o(goToPersonalInfo),
        g: common_vendor.unref(userStore).isApproved
      }, common_vendor.unref(userStore).isApproved ? {
        h: common_vendor.o(goToCertificates)
      } : {}, {
        i: common_vendor.o(goToFeedback),
        j: common_vendor.o(goToAbout),
        k: common_vendor.o(handleLogout)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dd383ca2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/profile.js.map
