{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-gap/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 背景颜色（默认transparent）\n        bgColor: {\n            type: String,\n            default: () => defProps.gap.bgColor\n        },\n        // 分割槽高度，单位px（默认30）\n        height: {\n            type: [String, Number],\n            default: () => defProps.gap.height\n        },\n        // 与上一个组件的距离\n        marginTop: {\n            type: [String, Number],\n            default: () => defProps.gap.marginTop\n        },\n        // 与下一个组件的距离\n        marginBottom: {\n            type: [String, Number],\n            default: () => defProps.gap.marginBottom\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,IAAI;AAAA,IAC/B;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,IAAI;AAAA,IAC/B;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,IAAI;AAAA,IAC/B;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,IAAI;AAAA,IAC/B;AAAA,EACJ;AACL,CAAC;;"}