"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_checkbox2 = common_vendor.resolveComponent("u-checkbox");
  const _easycom_u_checkbox_group2 = common_vendor.resolveComponent("u-checkbox-group");
  const _easycom_u_text2 = common_vendor.resolveComponent("u-text");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  const _easycom_u_toast2 = common_vendor.resolveComponent("u-toast");
  (_easycom_u_icon2 + _easycom_u_button2 + _easycom_u_checkbox2 + _easycom_u_checkbox_group2 + _easycom_u_text2 + _easycom_u_modal2 + _easycom_u_toast2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_checkbox = () => "../../uni_modules/uview-plus/components/u-checkbox/u-checkbox.js";
const _easycom_u_checkbox_group = () => "../../uni_modules/uview-plus/components/u-checkbox-group/u-checkbox-group.js";
const _easycom_u_text = () => "../../uni_modules/uview-plus/components/u-text/u-text.js";
const _easycom_u_modal = () => "../../uni_modules/uview-plus/components/u-modal/u-modal.js";
const _easycom_u_toast = () => "../../uni_modules/uview-plus/components/u-toast/u-toast.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_button + _easycom_u_checkbox + _easycom_u_checkbox_group + _easycom_u_text + _easycom_u_modal + _easycom_u_toast)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { setProfile } = userStore;
    const agreementValue = common_vendor.ref([]);
    const agreedToTerms = common_vendor.computed(() => agreementValue.value.includes("agreed"));
    const isLoading = common_vendor.ref(false);
    const showUserAgreementModal = common_vendor.ref(false);
    const showPrivacyPolicyModal = common_vendor.ref(false);
    const toastRef = common_vendor.ref(null);
    const modalTextSize = common_vendor.computed(() => 14);
    const modalTextLineHeight = common_vendor.computed(() => 22);
    const wechatIconStyle = common_vendor.computed(() => ({
      marginRight: "8rpx"
    }));
    const loginButtonStyle = common_vendor.computed(() => ({
      width: "80%",
      height: "88rpx",
      backgroundColor: agreedToTerms.value ? "rgba(255, 255, 255, 0.2)" : "rgba(255, 255, 255, 0.1)",
      borderRadius: "44rpx",
      border: "none",
      transition: "all 0.3s ease"
    }));
    const userAgreementContent = common_vendor.computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`);
    const privacyPolicyContent = common_vendor.computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`);
    function handleAgreementChange(values) {
      agreementValue.value = values;
    }
    function showUserAgreement() {
      showUserAgreementModal.value = true;
    }
    function showPrivacyPolicy() {
      showPrivacyPolicyModal.value = true;
    }
    function showToast(title, type = "info") {
      if (toastRef.value) {
        toastRef.value.show({
          title,
          type,
          duration: type === "success" ? 1500 : 2e3
        });
      }
    }
    async function handleWxLogin() {
      if (!agreedToTerms.value) {
        showToast("请先同意用户协议", "warning");
        return;
      }
      isLoading.value = true;
      try {
        const loginResult = await new Promise((resolve, reject) => {
          common_vendor.index.login({
            provider: "weixin",
            success: resolve,
            fail: reject
          });
        });
        const loginParams = {
          code: loginResult.code
        };
        const userInfo = await src_api_modules_user.wxLogin(loginParams);
        setProfile(userInfo);
        showToast("登录成功", "success");
        setTimeout(() => {
          navigateByUserStatus(userInfo.status);
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:265", "微信登录失败:", error);
        showToast("登录失败，请重试", "error");
      } finally {
        isLoading.value = false;
      }
    }
    function navigateByUserStatus(status) {
      switch (status) {
        case "approved":
          common_vendor.index.reLaunch({ url: "/pages/info/info" });
          break;
        case "pending":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "rejected":
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
          break;
        case "incomplete":
        default:
          common_vendor.index.navigateTo({ url: "/pages/register/register" });
          break;
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "account",
          size: "120",
          color: "#ffffff"
        }),
        b: common_vendor.p({
          name: "weixin-fill",
          size: "20",
          color: "#ffffff",
          customStyle: wechatIconStyle.value
        }),
        c: common_vendor.o(handleWxLogin),
        d: common_vendor.p({
          type: "primary",
          disabled: !agreedToTerms.value || isLoading.value,
          loading: isLoading.value,
          loadingText: "登录中...",
          customStyle: loginButtonStyle.value,
          shape: "round",
          size: "large",
          throttleTime: 1e3
        }),
        e: common_vendor.p({
          name: "agreed",
          label: "我已阅读并同意",
          activeColor: "#ffffff",
          iconColor: "#ffffff"
        }),
        f: common_vendor.o(handleAgreementChange),
        g: common_vendor.o(($event) => agreementValue.value = $event),
        h: common_vendor.p({
          disabled: isLoading.value,
          activeColor: "#ffffff",
          inactiveColor: "rgba(255,255,255,0.3)",
          iconColor: "#ffffff",
          size: "20",
          iconSize: "14",
          shape: "square",
          labelColor: "#ffffff",
          labelSize: "24",
          placement: "row",
          modelValue: agreementValue.value
        }),
        i: common_vendor.o(showUserAgreement),
        j: common_vendor.o(showPrivacyPolicy),
        k: common_vendor.p({
          text: userAgreementContent.value,
          size: modalTextSize.value,
          color: "#212121",
          lineHeight: modalTextLineHeight.value
        }),
        l: common_vendor.o(($event) => showUserAgreementModal.value = false),
        m: common_vendor.o(($event) => showUserAgreementModal.value = $event),
        n: common_vendor.p({
          title: "用户服务协议",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showUserAgreementModal.value
        }),
        o: common_vendor.p({
          text: privacyPolicyContent.value,
          size: modalTextSize.value,
          color: "#212121",
          lineHeight: modalTextLineHeight.value
        }),
        p: common_vendor.o(($event) => showPrivacyPolicyModal.value = false),
        q: common_vendor.o(($event) => showPrivacyPolicyModal.value = $event),
        r: common_vendor.p({
          title: "隐私政策",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showPrivacyPolicyModal.value
        }),
        s: common_vendor.sr(toastRef, "e4e4508d-9", {
          "k": "toastRef"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
