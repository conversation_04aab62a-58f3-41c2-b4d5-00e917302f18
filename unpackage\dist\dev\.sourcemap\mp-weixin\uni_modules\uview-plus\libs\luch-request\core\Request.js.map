{"version": 3, "file": "Request.js", "sources": ["uni_modules/uview-plus/libs/luch-request/core/Request.js"], "sourcesContent": ["/**\r\n * @Class Request\r\n * @description luch-request http请求插件\r\n * @version 3.0.7\r\n * <AUTHOR>\r\n * @Date 2021-09-04\r\n * @Email <EMAIL>\r\n * 文档: https://www.quanzhan.co/luch-request/\r\n * github: https://github.com/lei-mu/luch-request\r\n * DCloud: http://ext.dcloud.net.cn/plugin?id=392\r\n * HBuilderX: beat-3.0.4 alpha-3.0.4\r\n */\r\n\r\nimport dispatchRequest from './dispatchRequest'\r\nimport InterceptorManager from './InterceptorManager'\r\nimport mergeConfig from './mergeConfig'\r\nimport defaults from './defaults'\r\nimport { isPlainObject } from '../utils'\r\nimport clone from '../utils/clone'\r\n\r\nexport default class Request {\r\n    /**\r\n   * @param {Object} arg - 全局配置\r\n   * @param {String} arg.baseURL - 全局根路径\r\n   * @param {Object} arg.header - 全局header\r\n   * @param {String} arg.method = [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE] - 全局默认请求方式\r\n   * @param {String} arg.dataType = [json] - 全局默认的dataType\r\n   * @param {String} arg.responseType = [text|arraybuffer] - 全局默认的responseType。支付宝小程序不支持\r\n   * @param {Object} arg.custom - 全局默认的自定义参数\r\n   * @param {Number} arg.timeout - 全局默认的超时时间，单位 ms。默认60000。H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)、微信小程序（2.10.0）、支付宝小程序\r\n   * @param {Boolean} arg.sslVerify - 全局默认的是否验证 ssl 证书。默认true.仅App安卓端支持（HBuilderX 2.3.3+）\r\n   * @param {Boolean} arg.withCredentials - 全局默认的跨域请求时是否携带凭证（cookies）。默认false。仅H5支持（HBuilderX 2.6.15+）\r\n   * @param {Boolean} arg.firstIpv4 - 全DNS解析时优先使用ipv4。默认false。仅 App-Android 支持 (HBuilderX 2.8.0+)\r\n   * @param {Function(statusCode):Boolean} arg.validateStatus - 全局默认的自定义验证器。默认statusCode >= 200 && statusCode < 300\r\n   */\r\n    constructor(arg = {}) {\n\t\t// console.info('初始化luch-request')\r\n        if (!isPlainObject(arg)) {\r\n            arg = {}\r\n            console.warn('设置全局参数必须接收一个Object')\r\n        }\r\n        this.config = clone({ ...defaults, ...arg })\r\n        this.interceptors = {\r\n            request: new InterceptorManager(),\r\n            response: new InterceptorManager()\r\n        }\r\n    }\r\n\r\n    /**\r\n   * @Function\r\n   * @param {Request~setConfigCallback} f - 设置全局默认配置\r\n   */\r\n    setConfig(f) {\r\n        this.config = f(this.config)\r\n    }\r\n\r\n    middleware(config) {\r\n        config = mergeConfig(this.config, config)\r\n        const chain = [dispatchRequest, undefined]\r\n        let promise = Promise.resolve(config)\r\n\r\n        this.interceptors.request.forEach((interceptor) => {\r\n            chain.unshift(interceptor.fulfilled, interceptor.rejected)\r\n        })\r\n\r\n        this.interceptors.response.forEach((interceptor) => {\r\n            chain.push(interceptor.fulfilled, interceptor.rejected)\r\n        })\r\n\r\n        while (chain.length) {\r\n            promise = promise.then(chain.shift(), chain.shift())\r\n        }\r\n\r\n        return promise\r\n    }\r\n\r\n    /**\r\n   * @Function\r\n   * @param {Object} config - 请求配置项\r\n   * @prop {String} options.url - 请求路径\r\n   * @prop {Object} options.data - 请求参数\r\n   * @prop {Object} [options.responseType = config.responseType] [text|arraybuffer] - 响应的数据类型\r\n   * @prop {Object} [options.dataType = config.dataType] - 如果设为 json，会尝试对返回的数据做一次 JSON.parse\r\n   * @prop {Object} [options.header = config.header] - 请求header\r\n   * @prop {Object} [options.method = config.method] - 请求方法\r\n   * @returns {Promise<unknown>}\r\n   */\r\n    request(config = {}) {\r\n        return this.middleware(config)\r\n    }\r\n\r\n    get(url, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            method: 'GET',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    post(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'POST',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #ifndef MP-ALIPAY\r\n    put(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'PUT',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-BAIDU\r\n    delete(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'DELETE',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    // #ifdef H5 || MP-WEIXIN\r\n    connect(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'CONNECT',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    // #ifdef  H5 || MP-WEIXIN || MP-BAIDU\r\n    head(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'HEAD',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-BAIDU\r\n    options(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'OPTIONS',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    // #ifdef H5 || MP-WEIXIN\r\n    trace(url, data, options = {}) {\r\n        return this.middleware({\r\n            url,\r\n            data,\r\n            method: 'TRACE',\r\n            ...options\r\n        })\r\n    }\r\n\r\n    // #endif\r\n\r\n    upload(url, config = {}) {\r\n        config.url = url\r\n        config.method = 'UPLOAD'\r\n        return this.middleware(config)\r\n    }\r\n\r\n    download(url, config = {}) {\r\n        config.url = url\r\n        config.method = 'DOWNLOAD'\r\n        return this.middleware(config)\r\n    }\r\n}\r\n\r\n/**\r\n * setConfig回调\r\n * @return {Object} - 返回操作后的config\r\n * @callback Request~setConfigCallback\r\n * @param {Object} config - 全局默认config\r\n */\r\n"], "names": ["isPlainObject", "uni", "clone", "defaults", "InterceptorManager", "mergeConfig", "dispatchRequest"], "mappings": ";;;;;;;;AAoBe,MAAM,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAezB,YAAY,MAAM,IAAI;AAElB,QAAI,CAACA,6CAAAA,cAAc,GAAG,GAAG;AACrB,YAAM,CAAE;AACRC,oBAAAA,MAAa,MAAA,QAAA,kEAAA,oBAAoB;AAAA,IACpC;AACD,SAAK,SAASC,mDAAK,MAAC,EAAE,GAAGC,qDAAQ,UAAE,GAAG,KAAK;AAC3C,SAAK,eAAe;AAAA,MAChB,SAAS,IAAIC,+DAAAA,mBAAoB;AAAA,MACjC,UAAU,IAAIA,+DAAAA,mBAAoB;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,UAAU,GAAG;AACT,SAAK,SAAS,EAAE,KAAK,MAAM;AAAA,EAC9B;AAAA,EAED,WAAW,QAAQ;AACf,aAASC,wDAAW,YAAC,KAAK,QAAQ,MAAM;AACxC,UAAM,QAAQ,CAACC,4DAAe,iBAAE,MAAS;AACzC,QAAI,UAAU,QAAQ,QAAQ,MAAM;AAEpC,SAAK,aAAa,QAAQ,QAAQ,CAAC,gBAAgB;AAC/C,YAAM,QAAQ,YAAY,WAAW,YAAY,QAAQ;AAAA,IACrE,CAAS;AAED,SAAK,aAAa,SAAS,QAAQ,CAAC,gBAAgB;AAChD,YAAM,KAAK,YAAY,WAAW,YAAY,QAAQ;AAAA,IAClE,CAAS;AAED,WAAO,MAAM,QAAQ;AACjB,gBAAU,QAAQ,KAAK,MAAM,SAAS,MAAM,OAAO;AAAA,IACtD;AAED,WAAO;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaD,QAAQ,SAAS,IAAI;AACjB,WAAO,KAAK,WAAW,MAAM;AAAA,EAChC;AAAA,EAED,IAAI,KAAK,UAAU,IAAI;AACnB,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAED,KAAK,KAAK,MAAM,UAAU,CAAA,GAAI;AAC1B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAGD,IAAI,KAAK,MAAM,UAAU,CAAA,GAAI;AACzB,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAKD,OAAO,KAAK,MAAM,UAAU,CAAA,GAAI;AAC5B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAKD,QAAQ,KAAK,MAAM,UAAU,CAAA,GAAI;AAC7B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAKD,KAAK,KAAK,MAAM,UAAU,CAAA,GAAI;AAC1B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAKD,QAAQ,KAAK,MAAM,UAAU,CAAA,GAAI;AAC7B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAKD,MAAM,KAAK,MAAM,UAAU,CAAA,GAAI;AAC3B,WAAO,KAAK,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,MACR,GAAG;AAAA,IACf,CAAS;AAAA,EACJ;AAAA,EAID,OAAO,KAAK,SAAS,IAAI;AACrB,WAAO,MAAM;AACb,WAAO,SAAS;AAChB,WAAO,KAAK,WAAW,MAAM;AAAA,EAChC;AAAA,EAED,SAAS,KAAK,SAAS,IAAI;AACvB,WAAO,MAAM;AACb,WAAO,SAAS;AAChB,WAAO,KAAK,WAAW,MAAM;AAAA,EAChC;AACL;;"}