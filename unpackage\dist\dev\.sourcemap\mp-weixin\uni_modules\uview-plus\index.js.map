{"version": 3, "file": "index.js", "sources": ["uni_modules/uview-plus/index.js"], "sourcesContent": ["// 看到此报错，是因为没有配置vite.config.js的【transpileDependencies】\n// const pleaseSetTranspileDependencies = {}, babelTest = pleaseSetTranspileDependencies?.test\n\n// 引入全局mixin\nimport { mixin } from './libs/mixin/mixin.js'\n// 小程序特有的mixin\nimport { mpMixin } from './libs/mixin/mpMixin.js'\n\n// 路由封装\nimport route from './libs/util/route.js'\n// 颜色渐变相关,colorGradient-颜色渐变,hexToRgb-十六进制颜色转rgb颜色,rgbToHex-rgb转十六进制\nimport colorGradient from './libs/function/colorGradient.js'\n\n// 规则检验\nimport test from './libs/function/test.js'\n// 防抖方法\nimport debounce from './libs/function/debounce.js'\n// 节流方法\nimport throttle from './libs/function/throttle.js'\n// 浮点计算\nimport calc from './libs/function/calc.js'\n// 浮点计算\nimport digit from './libs/function/digit.js'\n// 公共文件写入的方法\nimport index from './libs/function/index.js'\n\n// 配置信息\nimport config from './libs/config/config.js'\n// props配置信息\nimport props from './libs/config/props.js'\n// 各个需要fixed的地方的z-index配置文件\nimport zIndex from './libs/config/zIndex.js'\n// 关于颜色的配置，特殊场景使用\nimport color from './libs/config/color.js'\n// 平台\nimport platform from './libs/function/platform'\n\n// http\nimport http from './libs/function/http.js'\n\n// 导出\nlet themeType = ['primary', 'success', 'error', 'warning', 'info'];\nexport { route, http, debounce, throttle, calc, digit, platform, themeType, mixin, mpMixin, props, color, test, zIndex }\nexport * from './libs/function/index.js'\nexport * from './libs/function/colorGradient.js'\n\n/**\n * @description 修改uView内置属性值\n * @param {object} props 修改内置props属性\n * @param {object} config 修改内置config属性\n * @param {object} color 修改内置color属性\n * @param {object} zIndex 修改内置zIndex属性\n */\nexport function setConfig(configs) {\n\tindex.shallowMerge(config, configs.config || {})\n\tindex.shallowMerge(props, configs.props || {})\n\tindex.shallowMerge(color, configs.color || {})\n\tindex.shallowMerge(zIndex, configs.zIndex || {})\n}\nindex.setConfig = setConfig\n\nconst $u = {\n    route,\n    date: index.timeFormat, // 另名date\n    colorGradient: colorGradient.colorGradient,\n    hexToRgb: colorGradient.hexToRgb,\n    rgbToHex: colorGradient.rgbToHex,\n    colorToRgba: colorGradient.colorToRgba,\n    test,\n    type: themeType,\n    http,\n    config, // uview-plus配置信息相关，比如版本号\n    zIndex,\n    debounce,\n    throttle,\n\tcalc,\n    mixin,\n    mpMixin,\n    props,\n    ...index,\n    color,\n    platform\n}\n\nexport const mount$u = function() {\n    uni.$u = $u\n}\n\nfunction toCamelCase(str) {\n    return str.replace(/-([a-z])/g, function(match, group1) {\n      return group1.toUpperCase();\n    }).replace(/^[a-z]/, function(match) {\n      return match.toUpperCase();\n    });\n}\n\n// #ifdef APP || H5\nconst importFn = import.meta.glob('./components/u-*/u-*.vue', { eager: true })\nlet components = [];\n\n// 批量注册全局组件\nfor (const key in importFn) {\n    let component = importFn[key].default;\n    if (component.name && component.name.indexOf('u--') !== 0) {\n        component.install = function (Vue) {\n            Vue.component(name, component);\n        };\n        \n        // 导入组件\n        components.push(component);\n    }\n}\n// #endif\n\nconst install = (Vue, upuiParams = '') => {\n    // #ifdef APP || H5\n    components.forEach(function(component) {\n        const name = component.name.replace(/u-([a-zA-Z0-9-_]+)/g, 'up-$1');\n\t\tif (name != component.name) {\n\t\t\tVue.component(component.name, component); \n\t\t}\n        Vue.component(name, component); \n    });\n    // #endif\n\t\n\t// 初始化\n\tif (upuiParams) {\n\t\tuni.upuiParams = upuiParams\n\t\tlet temp = upuiParams()\n\t\tif (temp.httpIns) {\n\t\t\ttemp.httpIns(http)\n\t\t}\n\t\tif (temp.options) {\n\t\t\tsetConfig(temp.options)\n\t\t}\n\t}\n\n    // 同时挂载到uni和Vue.prototype中\n    // $u挂载到uni对象上\n    uni.$u = $u\n\n    // #ifndef APP-NVUE\n    // 只有vue，挂载到Vue.prototype才有意义，因为nvue中全局Vue.prototype和Vue.mixin是无效的\n    Vue.config.globalProperties.$u = $u\n    Vue.mixin(mixin)\n    // #endif\n}\n\nexport default {\n    install\n}\n"], "names": ["index", "config", "props", "color", "zIndex", "route", "colorGradient", "test", "http", "debounce", "throttle", "calc", "mixin", "mpMixin", "platform", "uni"], "mappings": ";;;;;;;;;;;;;;;;;AAyCA,IAAI,YAAY,CAAC,WAAW,WAAW,SAAS,WAAW,MAAM;AAY1D,SAAS,UAAU,SAAS;AAClCA,4CAAAA,MAAM,aAAaC,yCAAAA,QAAQ,QAAQ,UAAU,CAAA,CAAE;AAC/CD,4CAAAA,MAAM,aAAaE,wCAAAA,OAAO,QAAQ,SAAS,CAAA,CAAE;AAC7CF,4CAAAA,MAAM,aAAaG,wCAAAA,OAAO,QAAQ,SAAS,CAAA,CAAE;AAC7CH,4CAAAA,MAAM,aAAaI,yCAAAA,QAAQ,QAAQ,UAAU,CAAA,CAAE;AAChD;AACAJ,0CAAK,MAAC,YAAY;AAElB,MAAM,KAAK;AAAA,EACX,OAAIK,sCAAK;AAAA,EACL,MAAML,0CAAK,MAAC;AAAA;AAAA,EACZ,eAAeM,kDAAa,cAAC;AAAA,EAC7B,UAAUA,kDAAa,cAAC;AAAA,EACxB,UAAUA,kDAAa,cAAC;AAAA,EACxB,aAAaA,kDAAa,cAAC;AAAA,EAC/B,MAAIC,yCAAI;AAAA,EACJ,MAAM;AAAA,EACV,MAAIC,yCAAI;AAAA,EACR,QAAIP,yCAAM;AAAA;AAAA,EACV,QAAIG,yCAAM;AAAA,EACV,UAAIK,6CAAQ;AAAA,EACZ,UAAIC,6CAAQ;AAAA,EACZ,MAACC,yCAAI;AAAA,EACL,OAAIC,uCAAK;AAAA,EACT,SAAIC,yCAAO;AAAA,EACX,OAAIX,wCAAK;AAAA,EACL,GAAGF,0CAAK;AAAA,EACZ,OAAIG,wCAAK;AAAA,EACT,UAAIW,6CAAQ;AACZ;AAgCA,MAAM,UAAU,CAAC,KAAK,aAAa,OAAO;AAYzC,MAAI,YAAY;AACfC,kBAAG,MAAC,aAAa;AACjB,QAAI,OAAO,WAAY;AACvB,QAAI,KAAK,SAAS;AACjB,WAAK,QAAQP,6CAAI;AAAA,IACjB;AACD,QAAI,KAAK,SAAS;AACjB,gBAAU,KAAK,OAAO;AAAA,IACtB;AAAA,EACD;AAIEO,gBAAG,MAAC,KAAK;AAIT,MAAI,OAAO,iBAAiB,KAAK;AACjC,MAAI,MAAMH,4CAAK;AAEnB;AAEA,MAAe,YAAA;AAAA,EACX;AACJ;;"}