"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_info = require("../../src/api/modules/info.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "info",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const bannerList = common_vendor.ref([]);
    const announcementList = common_vendor.ref([]);
    const policyList = common_vendor.ref([]);
    const noticeList = common_vendor.ref([]);
    common_vendor.onMounted(() => {
      if (userStore.isApproved) {
        loadInfoData();
      }
    });
    async function loadInfoData() {
      try {
        const [announcements, policies, notices] = await Promise.all([
          src_api_modules_info.getAnnouncementList({ page: 1, pageSize: 5 }),
          src_api_modules_info.getPolicyList({ page: 1, pageSize: 3 }),
          src_api_modules_info.getNoticeList({ page: 1, pageSize: 3 })
        ]);
        announcementList.value = announcements;
        policyList.value = policies;
        noticeList.value = notices;
        bannerList.value = announcements.filter((item) => item.isTop).slice(0, 3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/info/info.vue:141", "加载信息数据失败:", error);
      }
    }
    function getStatusTipTitle() {
      if (userStore.isIncomplete)
        return "请完善个人资料";
      if (userStore.isPending)
        return "资料审核中";
      if (userStore.isRejected)
        return "资料审核未通过";
      return "身份认证异常";
    }
    function getStatusTipDesc() {
      if (userStore.isIncomplete)
        return "完善个人资料后即可查看最新信息";
      if (userStore.isPending)
        return "请耐心等待机构管理员审核";
      if (userStore.isRejected)
        return "请修改资料后重新提交";
      return "请联系管理员处理";
    }
    function goToRegister() {
      common_vendor.index.navigateTo({ url: "/pages/register/register" });
    }
    function goToList(type) {
      common_vendor.index.navigateTo({ url: `/pages/info/list?type=${type}` });
    }
    function goToDetail(item) {
      common_vendor.index.navigateTo({ url: `/pages/info/detail?id=${item.id}` });
    }
    function formatTime(timeStr) {
      const date = new Date(timeStr);
      const now = /* @__PURE__ */ new Date();
      const diff = now.getTime() - date.getTime();
      const days = Math.floor(diff / (1e3 * 60 * 60 * 24));
      if (days === 0)
        return "今天";
      if (days === 1)
        return "昨天";
      if (days < 7)
        return `${days}天前`;
      return date.toLocaleDateString();
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !common_vendor.unref(userStore).isApproved
      }, !common_vendor.unref(userStore).isApproved ? common_vendor.e({
        b: common_vendor.t(getStatusTipTitle()),
        c: common_vendor.t(getStatusTipDesc()),
        d: common_vendor.unref(userStore).isIncomplete
      }, common_vendor.unref(userStore).isIncomplete ? {
        e: common_vendor.o(goToRegister)
      } : {}) : {
        f: common_vendor.f(bannerList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.o(($event) => goToDetail(item), item.id),
            c: item.id
          };
        }),
        g: common_vendor.o(($event) => goToList("announcement")),
        h: common_vendor.f(announcementList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: item.isTop
          }, item.isTop ? {} : {}, {
            c: common_vendor.t(formatTime(item.publishTime)),
            d: item.id,
            e: common_vendor.o(($event) => goToDetail(item), item.id)
          });
        }),
        i: common_vendor.o(($event) => goToList("policy")),
        j: common_vendor.f(policyList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(formatTime(item.publishTime)),
            c: item.id,
            d: common_vendor.o(($event) => goToDetail(item), item.id)
          };
        }),
        k: common_vendor.o(($event) => goToList("notice")),
        l: common_vendor.f(noticeList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(formatTime(item.publishTime)),
            c: item.id,
            d: common_vendor.o(($event) => goToDetail(item), item.id)
          };
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f52d2d81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/info/info.js.map
