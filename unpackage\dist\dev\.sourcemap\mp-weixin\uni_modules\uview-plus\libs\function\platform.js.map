{"version": 3, "file": "platform.js", "sources": ["uni_modules/uview-plus/libs/function/platform.js"], "sourcesContent": ["/**\n * 注意：\n * 此部分内容，在vue-cli模式下，需要在vue.config.js加入如下内容才有效：\n * module.exports = {\n *     transpileDependencies: ['uview-v2']\n * }\n */\n\nlet platform = 'none'\n\n// #ifdef VUE3\nplatform = 'vue3'\n// #endif\n\n// #ifdef VUE2\nplatform = 'vue2'\n// #endif\n\n// #ifdef APP-PLUS\nplatform = 'plus'\n// #endif\n\n// #ifdef APP-NVUE\nplatform = 'nvue'\n// #endif\n\n// #ifdef H5\nplatform = 'h5'\n// #endif\n\n// #ifdef MP\nplatform = 'mp'\n// #endif\n\n// #ifdef MP-WEIXIN\nplatform = 'weixin'\n// #endif\n\n// #ifdef MP-ALIPAY\nplatform = 'alipay'\n// #endif\n\n// #ifdef MP-BAIDU\nplatform = 'baidu'\n// #endif\n\n// #ifdef MP-TOUTIAO\nplatform = 'toutiao'\n// #endif\n\n// #ifdef MP-QQ\nplatform = 'qq'\n// #endif\n\n// #ifdef MP-KUAISHOU\nplatform = 'kuaishou'\n// #endif\n\n// #ifdef MP-360\nplatform = '360'\n// #endif\n\n// #ifdef QUICKAPP-WEBVIEW\nplatform = 'quickapp-webview'\n// #endif\n\n// #ifdef QUICKAPP-WEBVIEW-HUAWEI\nplatform = 'quickapp-webview-huawei'\n// #endif\n\n// #ifdef QUICKAPP-WEBVIEW-UNION\nplatform = 'quckapp-webview-union'\n// #endif\n\nexport default platform\n"], "names": [], "mappings": ";AAQA,IAAI,WAAW;AAGf,WAAW;AAoBX,WAAW;AAIX,WAAW;AAuCX,MAAe,aAAA;;"}