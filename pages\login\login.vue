<template>
  <view class="login-container">
    <!-- 内容区域 -->
    <view class="content">
      <!-- 品牌logo -->
      <view class="logo">
        <u-icon name="account" size="120" color="#ffffff" />
      </view>
      
      <!-- 标题区域 -->
      <view class="title-area">
        <text class="main-title">疾控考试系统</text>
        <text class="sub-title">医护任职资格考试平台</text>
      </view>
      
      <!-- 微信登录按钮 -->
      <view class="login-btn-container">
        <u-button
          type="primary"
          :disabled="!agreedToTerms || isLoading"
          :loading="isLoading"
          loadingText="登录中..."
          :customStyle="loginButtonStyle"
          shape="round"
          size="large"
          :throttleTime="1000"
          @click="handleWxLogin"
        >
          <view class="btn-content">
            <u-icon name="weixin-fill" size="20" color="#ffffff" :customStyle="wechatIconStyle" />
            <text class="btn-text">微信授权登录</text>
          </view>
        </u-button>
      </view>
      
      <!-- 协议确认 -->
      <view class="agreement">
        <u-checkbox-group
          v-model="agreementValue"
          :disabled="isLoading"
          activeColor="#ffffff"
          inactiveColor="rgba(255,255,255,0.3)"
          iconColor="#ffffff"
          size="20"
          iconSize="14"
          shape="square"
          labelColor="#ffffff"
          labelSize="24"
          placement="row"
          @change="handleAgreementChange"
        >
          <u-checkbox
            name="agreed"
            label="我已阅读并同意"
            activeColor="#ffffff"
            iconColor="#ffffff"
          />
        </u-checkbox-group>
        <view class="agreement-links">
          <text class="link" @click="showUserAgreement">《用户服务协议》</text>
          <text class="agreement-text">和</text>
          <text class="link" @click="showPrivacyPolicy">《隐私政策》</text>
        </view>
      </view>
    </view>

    <!-- 用户协议模态框 -->
    <u-modal
      v-model="showUserAgreementModal"
      title="用户服务协议"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showUserAgreementModal = false"
    >
      <view class="modal-content">
        <u-text
          :text="userAgreementContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </u-modal>

    <!-- 隐私政策模态框 -->
    <u-modal
      v-model="showPrivacyPolicyModal"
      title="隐私政策"
      :showCancelButton="false"
      confirmText="我知道了"
      @confirm="showPrivacyPolicyModal = false"
    >
      <view class="modal-content">
        <u-text
          :text="privacyPolicyContent"
          :size="modalTextSize"
          color="#212121"
          :lineHeight="modalTextLineHeight"
        />
      </view>
    </u-modal>

    <!-- Toast 消息提示 -->
    <u-toast ref="toastRef" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/src/stores/modules/user'
import { wxLogin } from '@/src/api/modules/user'
import type { LoginParams, UserInfo } from '@/src/types/api'

// ==================== Interfaces ====================
interface ToastInstance {
  show: (options: {
    title: string
    type?: 'success' | 'error' | 'warning' | 'info'
    duration?: number
  }) => void
}

// ==================== Store ====================
const userStore = useUserStore()
const { profile } = storeToRefs(userStore)
const { setProfile } = userStore

// ==================== 响应式数据 ====================
/** 协议确认值（数组形式，符合u-checkbox-group要求） */
const agreementValue = ref<string[]>([])
/** 是否同意用户协议（计算属性） */
const agreedToTerms = computed(() => agreementValue.value.includes('agreed'))
/** 登录加载状态 */
const isLoading = ref<boolean>(false)
/** 显示用户协议模态框 */
const showUserAgreementModal = ref<boolean>(false)
/** 显示隐私政策模态框 */
const showPrivacyPolicyModal = ref<boolean>(false)

// ==================== Toast 引用 ====================
const toastRef = ref<ToastInstance | null>(null)

// ==================== 设计系统 ====================
/** 字体尺寸 - 使用px单位，符合uview-plus规范 */
const modalTextSize = computed(() => 14)        // 模态框文字

/** 行高 */
const modalTextLineHeight = computed(() => 22)

// ==================== 样式计算属性 ====================
/** 微信图标样式 */
const wechatIconStyle = computed(() => ({
  marginRight: '8rpx',
}))

/** 登录按钮样式 */
const loginButtonStyle = computed(() => ({
  width: '80%',
  height: '88rpx',
  backgroundColor: agreedToTerms.value ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
  borderRadius: '44rpx',
  border: 'none',
  transition: 'all 0.3s ease',
}))

/** 用户协议内容 */
const userAgreementContent = computed(() => `1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。`)

/** 隐私政策内容 */
const privacyPolicyContent = computed(() => `1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。`)

// ==================== 事件处理 ====================
/**
 * 处理协议确认变更
 * @param values 选中的值数组
 */
function handleAgreementChange(values: string[]): void {
  agreementValue.value = values
}

/**
 * 显示用户服务协议
 */
function showUserAgreement(): void {
  showUserAgreementModal.value = true
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy(): void {
  showPrivacyPolicyModal.value = true
}

/**
 * 显示Toast消息
 * @param title 消息标题
 * @param type 消息类型
 */
function showToast(title: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
  if (toastRef.value) {
    toastRef.value.show({
      title,
      type,
      duration: type === 'success' ? 1500 : 2000,
    })
  }
}

/**
 * 微信授权登录
 */
async function handleWxLogin(): Promise<void> {
  // 检查协议同意状态
  if (!agreedToTerms.value) {
    showToast('请先同意用户协议', 'warning')
    return
  }

  isLoading.value = true

  try {
    // 调用微信登录获取code
    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      })
    })

    // 构造登录参数
    const loginParams: LoginParams = {
      code: loginResult.code,
    }

    // 调用后端登录接口
    const userInfo: UserInfo = await wxLogin(loginParams)

    // 保存用户信息到Store
    setProfile(userInfo)

    // 登录成功提示
    showToast('登录成功', 'success')

    // 根据用户状态进行页面跳转
    setTimeout(() => {
      navigateByUserStatus(userInfo.status)
    }, 1500)

  } catch (error) {
    console.error('微信登录失败:', error)
    showToast('登录失败，请重试', 'error')
  } finally {
    isLoading.value = false
  }
}

/**
 * 根据用户状态进行页面跳转
 * @param status 用户状态
 */
function navigateByUserStatus(status: UserInfo['status']): void {
  switch (status) {
    case 'approved':
      // 已审核通过的正式用户，跳转到信息中心
      uni.reLaunch({ url: '/pages/info/info' })
      break
    case 'pending':
      // 待审核用户，跳转到个人中心查看审核状态
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'rejected':
      // 审核未通过用户，跳转到个人中心修改资料
      uni.reLaunch({ url: '/pages/profile/profile' })
      break
    case 'incomplete':
    default:
      // 未提交资料的新用户，跳转到注册页面
      uni.navigateTo({ url: '/pages/register/register' })
      break
  }
}
</script>

<style lang="scss" scoped>
// 导入项目设计系统变量
@import '@/src/styles/variables.scss';

/* ==================== 页面全局设置 ==================== */
page {
  height: 100%;
}

/* ==================== 主容器 ==================== */
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #7474BF 0%, #348AC7 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0 160rpx;
  box-sizing: border-box;
}

/* ==================== 内容区域 ==================== */
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateY(-20rpx);
}

/* ==================== Logo区域 ==================== */
.logo {
  width: 240rpx;
  height: 240rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60rpx;
}

/* ==================== 标题区域 ==================== */
.title-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.main-title {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.sub-title {
  font-size: 32rpx;
  color: #ffffff;
}

/* ==================== 登录按钮区域 ==================== */
.login-btn-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 80rpx;
}

.btn-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  color: #ffffff;
  font-size: 32rpx;
  margin-left: 16rpx;
}

/* ==================== 协议区域 ==================== */
.agreement {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.agreement-links {
  margin-top: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agreement-text {
  font-size: 24rpx;
  color: #ffffff;
  margin: 0 8rpx;
}

.link {
  font-size: 24rpx;
  color: #ffffff;
  text-decoration: underline;
}

/* ==================== 复选框样式优化 ==================== */
:deep(.u-checkbox-group) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.u-checkbox) {
  display: flex;
  align-items: center;
}

:deep(.u-checkbox__icon-wrap) {
  margin-right: 16rpx;
}

/* ==================== 模态框内容 ==================== */
.modal-content {
  padding: $spacing-lg 0;
  max-height: 600rpx;
  overflow-y: auto;
  line-height: 1.6;
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
  .logo {
    width: 200rpx;
    height: 200rpx;
  }
  
  .main-title {
    font-size: 44rpx;
  }
  
  .sub-title {
    font-size: 28rpx;
  }
}

@media screen and (max-width: 600rpx) {
  .content {
    padding: 0 $spacing-md;
  }
  
  .logo {
    width: 180rpx;
    height: 180rpx;
  }
  
  .main-title {
    font-size: 40rpx;
  }
  
  .sub-title {
    font-size: 26rpx;
  }
}
</style>
