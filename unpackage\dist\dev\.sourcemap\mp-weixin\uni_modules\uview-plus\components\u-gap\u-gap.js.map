{"version": 3, "file": "u-gap.js", "sources": ["uni_modules/uview-plus/components/u-gap/u-gap.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1nYXAvdS1nYXAudnVl"], "sourcesContent": ["<template>\n\t<view class=\"u-gap\" :style=\"[gapStyle]\"></view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit, deepMerge } from '../../libs/function/index.js';\n\t/**\n\t * gap 间隔槽\n\t * @description 该组件一般用于内容块之间的用一个灰色块隔开的场景，方便用户风格统一，减少工作量\n\t * @tutorial https://ijry.github.io/uview-plus/components/gap.html\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 （默认 'transparent' ）\n\t * @property {String | Number}\theight\t\t\t分割槽高度，单位px （默认 20 ）\n\t * @property {String | Number}\tmarginTop\t\t与前一个组件的距离，单位px（ 默认 0 ）\n\t * @property {String | Number}\tmarginBottom\t与后一个组件的距离，单位px （默认 0 ）\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t * \n\t * @example <u-gap height=\"80\" bg-color=\"#bbb\"></u-gap>\n\t */\n\texport default {\n\t\tname: \"u-gap\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tgapStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tbackgroundColor: this.bgColor,\n\t\t\t\t\theight: addUnit(this.height),\n\t\t\t\t\tmarginTop: addUnit(this.marginTop),\n\t\t\t\t\tmarginBottom: addUnit(this.marginBottom),\n\t\t\t\t}\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n</style>\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-gap/u-gap.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AAqBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,iDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,WAAW;AACV,YAAM,QAAQ;AAAA,QACb,iBAAiB,KAAK;AAAA,QACtB,QAAQC,0CAAAA,QAAQ,KAAK,MAAM;AAAA,QAC3B,WAAWA,0CAAAA,QAAQ,KAAK,SAAS;AAAA,QACjC,cAAcA,0CAAAA,QAAQ,KAAK,YAAY;AAAA,MACxC;AACA,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACD;;;;;;;;ACjCF,GAAG,gBAAgB,SAAS;"}