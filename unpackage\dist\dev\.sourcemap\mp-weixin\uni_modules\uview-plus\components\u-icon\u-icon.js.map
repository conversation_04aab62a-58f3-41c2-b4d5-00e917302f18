{"version": 3, "file": "u-icon.js", "sources": ["uni_modules/uview-plus/components/u-icon/u-icon.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1pY29uL3UtaWNvbi52dWU"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-icon\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"['u-icon--' + labelPos]\"\n\t>\n\t\t<image\n\t\t    class=\"u-icon__img\"\n\t\t    v-if=\"isImg\"\n\t\t    :src=\"name\"\n\t\t    :mode=\"imgMode\"\n\t\t    :style=\"[imgStyle, addStyle(customStyle)]\"\n\t\t></image>\n\t\t<text\n\t\t    v-else\n\t\t    class=\"u-icon__icon\"\n\t\t    :class=\"uClasses\"\n\t\t    :style=\"[iconStyle, addStyle(customStyle)]\"\n\t\t    :hover-class=\"hoverClass\"\n\t\t>{{icon}}</text>\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\n\t\t<text\n\t\t    v-if=\"label !== ''\" \n\t\t    class=\"u-icon__label\"\n\t\t    :style=\"{\n\t\t\tcolor: labelColor,\n\t\t\tfontSize: addUnit(labelSize),\n\t\t\tmarginLeft: labelPos == 'right' ? addUnit(space) : 0,\n\t\t\tmarginTop: labelPos == 'bottom' ? addUnit(space) : 0,\n\t\t\tmarginRight: labelPos == 'left' ? addUnit(space) : 0,\n\t\t\tmarginBottom: labelPos == 'top' ? addUnit(space) : 0,\n\t\t}\"\n\t\t>{{ label }}</text>\n\t</view>\n</template>\n\n<script>\n\t// 引入图标名称，已经对应的unicode\n\timport icons from './icons';\n\timport { props } from './props';\n\timport config from '../../libs/config/config';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle } from '../../libs/function/index';\n\t/**\n\t * icon 图标\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\n\t * @tutorial https://ijry.github.io/uview-plus/components/icon.html\n\t * @property {String}\t\t\tname\t\t\t图标名称，见示例图标集\n\t * @property {String}\t\t\tcolor\t\t\t图标颜色,可接受主题色 （默认 color['u-content-color'] ）\n\t * @property {String | Number}\tsize\t\t\t图标字体大小，单位px （默认 '16px' ）\n\t * @property {Boolean}\t\t\tbold\t\t\t是否显示粗体 （默认 false ）\n\t * @property {String | Number}\tindex\t\t\t点击图标的时候传递事件出去的index（用于区分点击了哪一个）\n\t * @property {String}\t\t\thoverClass\t\t图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网\n\t * @property {String}\t\t\tcustomPrefix\t自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）\n\t * @property {String | Number}\tlabel\t\t\t图标右侧的label文字\n\t * @property {String}\t\t\tlabelPos\t\tlabel相对于图标的位置，只能right或bottom （默认 'right' ）\n\t * @property {String | Number}\tlabelSize\t\tlabel字体大小，单位px （默认 '15px' ）\n\t * @property {String}\t\t\tlabelColor\t\t图标右侧的label文字颜色 （ 默认 color['u-content-color'] ）\n\t * @property {String | Number}\tspace\t\t\tlabel与图标的距离，单位px （默认 '3px' ）\n\t * @property {String}\t\t\timgMode\t\t\t图片的mode\n\t * @property {String | Number}\twidth\t\t\t显示图片小图标时的宽度\n\t * @property {String | Number}\theight\t\t\t显示图片小图标时的高度\n\t * @property {String | Number}\ttop\t\t\t\t图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）\n\t * @property {Boolean}\t\t\tstop\t\t\t是否阻止事件传播 （默认 false ）\n\t * @property {Object}\t\t\tcustomStyle\t\ticon的样式，对象形式\n\t * @event {Function} click 点击图标时触发\n\t * @event {Function} touchstart 事件触摸时触发\n\t * @example <u-icon name=\"photo\" color=\"#2979ff\" size=\"28\"></u-icon>\n\t */\n\texport default {\n\t\tname: 'u-icon',\n\t\tbeforeCreate() {\n\t\t\t\n\t\t\t// #ifdef APP-NVUE\n\t\t\t// nvue通过weex的dom模块引入字体，相关文档地址如下：\n\t\t\t// https://weex.apache.org/zh/docs/modules/dom.html#addrule\n\t\t\tconst domModule = weex.requireModule('dom');\n\t\t\tdomModule.addRule('fontFace', {\n\t\t\t\t'fontFamily': \"uicon-iconfont\",\n\t\t\t\t'src': `url('${config.iconUrl}')`\n\t\t\t});\n\t\t\tif (config.customIcon.family) {\n\t\t\t\tdomModule.addRule('fontFace', {\n\t\t\t\t\t'fontFamily': config.customIcon.family,\n\t\t\t\t\t'src': `url('${config.customIcon.url}')`\n\t\t\t\t});\n\t\t\t}\n\t\t\t// #endif\n\t\t\t// #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY\n\t\t\tuni.loadFontFace({\n\t\t\t\tfamily: 'uicon-iconfont',\n\t\t\t\tsource: 'url(\"' + config.iconUrl + '\")',\n\t\t\t\tsuccess() {\n\t\t\t\t\t// console.log('内置字体图标加载成功');\n\t\t\t\t},\n\t\t\t\tfail() {\n\t\t\t\t\tconsole.error('内置字体图标加载出错');\n\t\t\t\t}\n\t\t\t});\n\t\t\tif (config.customIcon.family) {\n\t\t\t\tuni.loadFontFace({\n\t\t\t\t\tfamily: config.customIcon.family,\n\t\t\t\t\tsource: 'url(\"' + config.customIcon.url + '\")',\n\t\t\t\t\tsuccess() {\n\t\t\t\t\t\t// console.log('扩展字体图标加载成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail() {\n\t\t\t\t\t\tconsole.error('扩展字体图标加载出错');\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\t// #endif\n\t\t\t// #ifdef APP-NVUE\n\t\t\tif (this.customFontFamily) {\n\t\t\t\tdomModule.addRule('fontFace', {\n\t\t\t\t\t'fontFamily': `${this.customPrefix}-${this.customFontFamily}`,\n\t\t\t\t\t'src': `url('${this.customFontUrl}')`\n\t\t\t\t})\n\t\t\t}\n        \t// #endif\n    \t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t}\n\t\t},\n\t\temits: ['click'],\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tuClasses() {\n\t\t\t\tlet classes = []\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t\t// uview-plus内置图标类名为u-iconfont\n\t\t\t\tif (this.customPrefix == 'uicon') {\n\t\t\t\t\tclasses.push('u-iconfont')\n\t\t\t\t} else {\n\t\t\t\t\t// 不能缺少这一步，否则自定义图标会无效\n\t\t\t\t\tclasses.push(this.customPrefix)\n\t\t\t\t}\n\t\t\t\t// 主题色，通过类配置\n\t\t\t\tif (this.color && config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t//#endif\n\t\t\t\treturn classes\n\t\t\t},\n\t\t\ticonStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle = {\n\t\t\t\t\tfontSize: addUnit(this.size),\n\t\t\t\t\tlineHeight: addUnit(this.size),\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\t\ttop: addUnit(this.top)\n\t\t\t\t}\n\t\t\t\tif (this.customPrefix !== 'uicon') {\n\t\t\t\t\tstyle.fontFamily = this.customPrefix\n\t\t\t\t}\n\t\t\t\t// 非主题色值时，才当作颜色值\n\t\t\t\tif (this.color && !config.type.includes(this.color)) style.color = this.color\n\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.name.indexOf('/') !== -1\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\n\t\t\t\tstyle.width = this.width ? addUnit(this.width) : addUnit(this.size)\n\t\t\t\tstyle.height = this.height ? addUnit(this.height) : addUnit(this.size)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 通过图标名，查找对应的图标\n\t\t\ticon() {\n\t\t\t\t// 使用自定义图标的时候页面上会把name属性也展示出来，所以在这里处理一下\n\t\t\t\tif (this.customPrefix !== \"uicon\") {\n\t\t\t\t\treturn config.customIcons[this.name] || this.name;\n\t\t\t\t}\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\n\t\t\t\treturn icons['uicon-' + this.name] || this.name\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tclickHandler(e) {\n\t\t\t\tthis.$emit('click', this.index, e)\n\t\t\t\t// 是否阻止事件冒泡\n\t\t\t\tthis.stop && this.preventEvent(e)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\n\t// 变量定义\n\t$u-icon-primary: $u-primary !default;\n\t$u-icon-success: $u-success !default;\n\t$u-icon-info: $u-info !default;\n\t$u-icon-warning: $u-warning !default;\n\t$u-icon-error: $u-error !default;\n\t$u-icon-label-line-height:1 !default;\n\n\t/* #ifdef MP-QQ || MP-TOUTIAO || MP-BAIDU || MP-KUAISHOU || MP-XHS */\n\t// 2025/04/09在App/微信/支付宝/鸿蒙元服务已改用uni.loadFontFace加载字体\n\t@font-face {\n\t\tfont-family: 'uicon-iconfont';\n\t\tsrc: url('https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf') format('truetype');\n\t}\n\t/* #endif */\n\n\t.u-icon {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\talign-items: center;\n\n\t\t&--left {\n\t\t\tflex-direction: row-reverse;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--right {\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--top {\n\t\t\tflex-direction: column-reverse;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&--bottom {\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&__icon {\n\t\t\tfont-family: uicon-iconfont;\n\t\t\tposition: relative;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&--primary {\n\t\t\t\tcolor: $u-icon-primary;\n\t\t\t}\n\n\t\t\t&--success {\n\t\t\t\tcolor: $u-icon-success;\n\t\t\t}\n\n\t\t\t&--error {\n\t\t\t\tcolor: $u-icon-error;\n\t\t\t}\n\n\t\t\t&--warning {\n\t\t\t\tcolor: $u-icon-warning;\n\t\t\t}\n\n\t\t\t&--info {\n\t\t\t\tcolor: $u-icon-info;\n\t\t\t}\n\t\t}\n\n\t\t&__img {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\theight: auto;\n\t\t\twill-change: transform;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__label {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tline-height: $u-icon-label-line-height;\n\t\t\t/* #endif */\n\t\t}\n\t}\n</style>\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-icon/u-icon.vue'\nwx.createComponent(Component)"], "names": ["uni", "config", "mpMixin", "mixin", "props", "addUnit", "icons", "addStyle"], "mappings": ";;;;;;;;AAsEC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,eAAe;AAkBdA,kBAAAA,MAAI,aAAa;AAAA,MAChB,QAAQ;AAAA,MACR,QAAQ,UAAUC,gDAAO,UAAU;AAAA,MACnC,UAAU;AAAA,MAET;AAAA,MACD,OAAO;AACND,sBAAAA,MAAc,MAAA,SAAA,6DAAA,YAAY;AAAA,MAC3B;AAAA,IACD,CAAC;AACD,QAAIC,yCAAM,OAAC,WAAW,QAAQ;AAC7BD,oBAAAA,MAAI,aAAa;AAAA,QAChB,QAAQC,yCAAAA,OAAO,WAAW;AAAA,QAC1B,QAAQ,UAAUA,yCAAAA,OAAO,WAAW,MAAM;AAAA,QAC1C,UAAU;AAAA,QAET;AAAA,QACD,OAAO;AACND,wBAAAA,MAAA,MAAA,SAAA,8DAAc,YAAY;AAAA,QAC3B;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EAUG;AAAA,EACJ,OAAO;AACN,WAAO,CACP;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,QAAQ,CAACE,yCAAAA,SAASC,uCAAK,OAAEC,kDAAK;AAAA,EAC9B,UAAU;AAAA,IACT,WAAW;AACV,UAAI,UAAU,CAAC;AACf,cAAQ,KAAK,KAAK,eAAe,MAAM,KAAK,IAAI;AAEhD,UAAI,KAAK,gBAAgB,SAAS;AACjC,gBAAQ,KAAK,YAAY;AAAA,aACnB;AAEN,gBAAQ,KAAK,KAAK,YAAY;AAAA,MAC/B;AAEA,UAAI,KAAK,SAASH,gDAAO,KAAK,SAAS,KAAK,KAAK;AAAG,gBAAQ,KAAK,mBAAmB,KAAK,KAAK;AAM9F,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,UAAI,QAAQ,CAAC;AACb,cAAQ;AAAA,QACP,UAAUI,0CAAAA,QAAQ,KAAK,IAAI;AAAA,QAC3B,YAAYA,0CAAAA,QAAQ,KAAK,IAAI;AAAA,QAC7B,YAAY,KAAK,OAAO,SAAS;AAAA;AAAA,QAEjC,KAAKA,0CAAAA,QAAQ,KAAK,GAAG;AAAA,MACtB;AACA,UAAI,KAAK,iBAAiB,SAAS;AAClC,cAAM,aAAa,KAAK;AAAA,MACzB;AAEA,UAAI,KAAK,SAAS,CAACJ,yCAAAA,OAAO,KAAK,SAAS,KAAK,KAAK;AAAG,cAAM,QAAQ,KAAK;AAExE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,KAAK,QAAQ,GAAG,MAAM;AAAA,IAClC;AAAA,IACD,WAAW;AACV,UAAI,QAAQ,CAAC;AAEb,YAAM,QAAQ,KAAK,QAAQI,0CAAO,QAAC,KAAK,KAAK,IAAIA,0CAAAA,QAAQ,KAAK,IAAI;AAClE,YAAM,SAAS,KAAK,SAASA,0CAAO,QAAC,KAAK,MAAM,IAAIA,0CAAAA,QAAQ,KAAK,IAAI;AACrE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,OAAO;AAEN,UAAI,KAAK,iBAAiB,SAAS;AAClC,eAAOJ,yCAAAA,OAAO,YAAY,KAAK,IAAI,KAAK,KAAK;AAAA,MAC9C;AAEA,aAAOK,6CAAAA,MAAM,WAAW,KAAK,IAAI,KAAK,KAAK;AAAA,IAC5C;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,0CAAQ;AAAA,IACR,SAAAF,0CAAO;AAAA,IACP,aAAa,GAAG;AACf,WAAK,MAAM,SAAS,KAAK,OAAO,CAAC;AAEjC,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IACjC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClMD,GAAG,gBAAgB,SAAS;"}