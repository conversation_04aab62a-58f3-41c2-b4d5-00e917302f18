{"version": 3, "file": "u-toast.js", "sources": ["uni_modules/uview-plus/components/u-toast/u-toast.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS10b2FzdC91LXRvYXN0LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"u-toast\">\n\t\t<u-overlay\n\t\t\t:show=\"isShow\"\n\t\t\t:zIndex=\"tmpConfig.overlay ? tmpConfig.zIndex : -1\"\n\t\t\t:custom-style=\"overlayStyle\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-toast__content\"\n\t\t\t\t:style=\"[contentStyle]\"\n\t\t\t\t:class=\"['u-type-' + tmpConfig.type, (tmpConfig.type === 'loading' || tmpConfig.loading) ?  'u-toast__content--loading' : '']\"\n\t\t\t>\n\t\t\t\t<u-loading-icon\n\t\t\t\t\tv-if=\"tmpConfig.type === 'loading'\"\n\t\t\t\t\tmode=\"circle\"\n\t\t\t\t\tcolor=\"rgb(255, 255, 255)\"\n\t\t\t\t\tinactiveColor=\"rgb(120, 120, 120)\"\n\t\t\t\t\tsize=\"25\"\n\t\t\t\t></u-loading-icon>\n\t\t\t\t<u-icon\n\t\t\t\t\tv-else-if=\"tmpConfig.type !== 'defalut' && iconName\"\n\t\t\t\t\t:name=\"iconName\"\n\t\t\t\t\tsize=\"17\"\n\t\t\t\t\t:color=\"tmpConfig.type\"\n\t\t\t\t\t:customStyle=\"iconStyle\"\n\t\t\t\t></u-icon>\n\t\t\t\t<u-gap\n\t\t\t\t\tv-if=\"tmpConfig.type === 'loading' || tmpConfig.loading\"\n\t\t\t\t\theight=\"12\"\n\t\t\t\t\tbgColor=\"transparent\"\n\t\t\t\t></u-gap>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-toast__content__text\"\n\t\t\t\t\t:class=\"['u-toast__content__text--' + tmpConfig.type]\"\n\t\t\t\t\tstyle=\"max-width: 400rpx;\"\n\t\t\t\t>{{ tmpConfig.message }}</text>\n\t\t\t</view>\n\t\t</u-overlay>\n\t</view>\n</template>\n\n<script>\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { os, getWindowInfo, deepMerge, type2icon } from '../../libs/function/index';\n\timport color from '../../libs/config/color';\n\timport { hexToRgb } from '../../libs/function/colorGradient';\n\t/**\n\t * toast 消息提示\n\t * @description 此组件表现形式类似uni的uni.showToastAPI，但也有不同的地方。\n\t * @tutorial https://ijry.github.io/uview-plus/components/toast.html\n\t * @property {String | Number}\tzIndex\t\ttoast展示时的zIndex值 (默认 10090 )\n\t * @property {Boolean}\t\t\tloading\t\t是否加载中 （默认 false ）\n\t * @property {String | Number}\tmessage\t\t显示的文字内容\n\t * @property {String}\t\t\ticon\t\t图标，或者绝对路径的图片\n\t * @property {String}\t\t\ttype\t\t主题类型 （默认 default）\n\t * @property {Boolean}\t\t\tshow\t\t是否显示该组件 （默认 false）\n\t * @property {Boolean}\t\t\toverlay\t\t是否显示透明遮罩，防止点击穿透 （默认 true ）\n\t * @property {String}\t\t\tposition\t位置 （默认 'center' ）\n\t * @property {Object}\t\t\tparams\t\t跳转的参数 \n\t * @property {String | Number}  duration\t展示时间，单位ms （默认 2000 ）\n\t * @property {Boolean}\t\t\tisTab\t\t是否返回的为tab页面 （默认 false ）\n\t * @property {String}\t\t\turl\t\t\ttoast消失后是否跳转页面，有则跳转，优先级高于back参数 \n\t * @property {Function}\t\t\tcomplete\t执行完后的回调函数 \n\t * @property {Boolean}\t\t\tback\t\t结束toast是否自动返回上一页 （默认 false ）\n\t * @property {Object}\t\t\tcustomStyle\t组件的样式，对象形式\n\t * @event {Function} show 显示toast，如需一进入页面就显示toast，请在onReady生命周期调用\n\t * @example <u-toast ref=\"uToast\" />\n\t */\n\texport default {\n\t\tname: 'u-toast',\n\t\tmixins: [mpMixin, mixin],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisShow: false,\n\t\t\t\ttimer: null, // 定时器\n\t\t\t\tconfig: {\n\t\t\t\t\tmessage: '', // 显示文本\n\t\t\t\t\ttype: '', // 主题类型，primary，success，error，warning，black\n\t\t\t\t\tzIndex: 10090, // 层级\n\t\t\t\t\tduration: 2000, // 显示的时间，毫秒\n\t\t\t\t\ticon: true, // 显示的图标\n\t\t\t\t\tposition: 'center', // toast出现的位置\n\t\t\t\t\tcomplete: null, // 执行完后的回调函数\n\t\t\t\t\toverlay: true, // 是否防止触摸穿透\n\t\t\t\t\tloading: false, // 是否加载中状态\n\t\t\t\t},\n\t\t\t\ttmpConfig: {}, // 将用户配置和内置配置合并后的临时配置变量\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ticonName() {\n\t\t\t\t// 只有不为none，并且type为error|warning|succes|info时候，才显示图标\n\t\t\t\tif(!this.tmpConfig.icon || this.tmpConfig.icon == 'none') {\n\t\t\t\t\treturn '';\n\t\t\t\t}\n\t\t\t\tif (this.tmpConfig.icon === true) {\n\t\t\t\t\tif (['error', 'warning', 'success', 'primary'].includes(this.tmpConfig.type)) {\n\t\t\t\t\t\treturn type2icon(this.tmpConfig.type)\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn ''\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\treturn this.tmpConfig.icon\n\t\t\t\t}\n\t\t\t},\n\t\t\toverlayStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tjustifyContent: 'center',\n\t\t\t\t\talignItems: 'center',\n\t\t\t\t\tdisplay: 'flex'\n\t\t\t\t}\n\t\t\t\t// 将遮罩设置为100%透明度，避免出现灰色背景\n\t\t\t\tstyle.backgroundColor = 'rgba(0, 0, 0, 0)'\n\t\t\t\treturn style\n\t\t\t},\n\t\t\ticonStyle() {\n\t\t\t\tconst style = {}\n\t\t\t\t// 图标需要一个右边距，以跟右边的文字有隔开的距离\n\t\t\t\tstyle.marginRight = '4px'\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// iOSAPP下，图标有1px的向下偏移，这里进行修正\n\t\t\t\tif (os() === 'ios') {\n\t\t\t\t\tstyle.marginTop = '-1px'\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tloadingIconColor() {\n\t\t\t\tlet colorTmp = 'rgb(255, 255, 255)'\n\t\t\t\tif (['error', 'warning', 'success', 'primary'].includes(this.tmpConfig.type)) {\n\t\t\t\t\t// loading-icon组件内部会对color参数进行一个透明度处理，该方法要求传入的颜色值\n\t\t\t\t\t// 必须为rgb格式的，所以这里做一个处理\n\t\t\t\t\tcolorTmp = hexToRgb(color[this.tmpConfig.type])\n\t\t\t\t}\n\t\t\t\treturn colorTmp\n\t\t\t},\n\t\t\t// 内容盒子的样式\n\t\t\tcontentStyle() {\n\t\t\t\tconst windowHeight = getWindowInfo().windowHeight, style = {}\n\t\t\t\tlet value = 0\n\t\t\t\t// 根据top和bottom，对Y轴进行窗体高度的百分比偏移\n\t\t\t\tif(this.tmpConfig.position === 'top') {\n\t\t\t\t\tvalue = - windowHeight * 0.25\n\t\t\t\t} else if(this.tmpConfig.position === 'bottom') {\n\t\t\t\t\tvalue = windowHeight * 0.25\n\t\t\t\t}\n\t\t\t\tstyle.transform = `translateY(${value}px)`\n\t\t\t\treturn style\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 通过主题的形式调用toast，批量生成方法函数\n\t\t\t['primary', 'success', 'error', 'warning', 'default', 'loading'].map(item => {\n\t\t\t\tthis[item] = message => this.show({\n\t\t\t\t\ttype: item,\n\t\t\t\t\tmessage\n\t\t\t\t})\n\t\t\t})\n\t\t},\n\t\tmethods: {\n\t\t\t// 显示toast组件，由父组件通过this.$refs.xxx.show(options)形式调用\n\t\t\tshow(options) {\n\t\t\t\t// 不将结果合并到this.config变量，避免多次调用u-toast，前后的配置造成混乱\n\t\t\t\tthis.tmpConfig = deepMerge(this.config, options)\n\t\t\t\t// 清除定时器\n\t\t\t\tthis.clearTimer()\n\t\t\t\tthis.isShow = true\n\t\t\t\t// -1时不自动关闭\n\t\t\t\tif (this.tmpConfig.duration !== -1) {\n\t\t\t\t\tthis.timer = setTimeout(() => {\n\t\t\t\t\t\t// 倒计时结束，清除定时器，隐藏toast组件\n\t\t\t\t\t\tthis.clearTimer()\n\t\t\t\t\t\t// 判断是否存在callback方法，如果存在就执行\n\t\t\t\t\t\ttypeof(this.tmpConfig.complete) === 'function' && this.tmpConfig.complete()\n\t\t\t\t\t}, this.tmpConfig.duration)\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 隐藏toast组件，由父组件通过this.$refs.xxx.hide()形式调用\n\t\t\thide() {\n\t\t\t\tthis.clearTimer()\n\t\t\t},\n\t\t\tclearTimer() {\n\t\t\t\tthis.isShow = false\n\t\t\t\t// 清除定时器\n\t\t\t\tclearTimeout(this.timer)\n\t\t\t\tthis.timer = null\n\t\t\t}\n\t\t},\n\t\tbeforeUnmount() {\n\t\t\tthis.clearTimer()\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\n\t$u-toast-color:#fff !default;\n\t$u-toast-border-radius:4px !default;\n\t$u-toast-border-background-color:#585858 !default;\n\t$u-toast-border-font-size:14px !default;\n\t$u-toast-border-padding:12px 20px !default;\n\t$u-toast-loading-border-padding: 20px 20px !default;\n\t$u-toast-content-text-color:#fff !default;\n\t$u-toast-content-text-font-size:15px !default;\n\t$u-toast-u-icon:10rpx !default;\n\t$u-toast-u-type-primary-color:$u-primary !default;\n\t$u-toast-u-type-primary-background-color:#ecf5ff !default;\n\t$u-toast-u-type-primary-border-color:rgb(215, 234, 254) !default;\n\t$u-toast-u-type-primary-border-width:1px !default;\n\t$u-toast-u-type-success-color: $u-success !default;\n\t$u-toast-u-type-success-background-color: #dbf1e1 !default;\n\t$u-toast-u-type-success-border-color: #BEF5C8 !default;\n\t$u-toast-u-type-success-border-width: 1px !default;\n\t$u-toast-u-type-error-color:$u-error !default;\n\t$u-toast-u-type-error-background-color:#fef0f0 !default;\n\t$u-toast-u-type-error-border-color:#fde2e2 !default;\n\t$u-toast-u-type-error-border-width: 1px !default;\n\t$u-toast-u-type-warning-color:$u-warning !default;\n\t$u-toast-u-type-warning-background-color:#fdf6ec !default;\n\t$u-toast-u-type-warning-border-color:#faecd8 !default;\n\t$u-toast-u-type-warning-border-width: 1px !default;\n\t$u-toast-u-type-default-color:#fff !default;\n\t$u-toast-u-type-default-background-color:#585858 !default;\n\n\t.u-toast {\n\t\t&__content {\n\t\t\t@include flex;\n\t\t\tpadding: $u-toast-border-padding;\n\t\t\tborder-radius: $u-toast-border-radius;\n\t\t\tbackground-color: $u-toast-border-background-color;\n\t\t\tcolor: $u-toast-color;\n\t\t\talign-items: center;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tmax-width: 600rpx;\n\t\t\t/* #endif */\n\t\t\tposition: relative;\n\n\t\t\t&--loading {\n\t\t\t\tflex-direction: column;\n\t\t\t\tpadding: $u-toast-loading-border-padding;\n\t\t\t}\n\n\t\t\t&__text {\n\t\t\t\tcolor: $u-toast-content-text-color;\n\t\t\t\tfont-size: $u-toast-content-text-font-size;\n\t\t\t\tline-height: $u-toast-content-text-font-size;\n\n\t\t\t\t&--default {\n\t\t\t\t\tcolor: $u-toast-content-text-color;\n\t\t\t\t}\n\n\t\t\t\t&--error {\n\t\t\t\t\tcolor: $u-error;\n\t\t\t\t}\n\n\t\t\t\t&--primary {\n\t\t\t\t\tcolor: $u-primary;\n\t\t\t\t}\n\n\t\t\t\t&--success {\n\t\t\t\t\tcolor: $u-success;\n\t\t\t\t}\n\n\t\t\t\t&--warning {\n\t\t\t\t\tcolor: $u-warning;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t.u-type-primary {\n\t\tcolor: $u-toast-u-type-primary-color;\n\t\tbackground-color: $u-toast-u-type-primary-background-color;\n\t\tborder-color: $u-toast-u-type-primary-border-color;\n\t\tborder-width: $u-toast-u-type-primary-border-width;\n\t}\n\n\t.u-type-success {\n\t\tcolor: $u-toast-u-type-success-color;\n\t\tbackground-color: $u-toast-u-type-success-background-color;\n\t\tborder-color: $u-toast-u-type-success-border-color;\n\t\tborder-width: 1px;\n\t}\n\n\t.u-type-error {\n\t\tcolor: $u-toast-u-type-error-color;\n\t\tbackground-color: $u-toast-u-type-error-background-color;\n\t\tborder-color: $u-toast-u-type-error-border-color;\n\t\tborder-width: $u-toast-u-type-error-border-width;\n\t}\n\n\t.u-type-warning {\n\t\tcolor: $u-toast-u-type-warning-color;\n\t\tbackground-color: $u-toast-u-type-warning-background-color;\n\t\tborder-color: $u-toast-u-type-warning-border-color;\n\t\tborder-width: 1px;\n\t}\n\n\t.u-type-default {\n\t\tcolor: $u-toast-u-type-default-color;\n\t\tbackground-color: $u-toast-u-type-default-background-color;\n\t}\n</style>\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-toast/u-toast.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "type2icon", "hexToRgb", "color", "getWindowInfo", "deepMerge"], "mappings": ";;;;;;;AAqEC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAO,SAAEC,4CAAK;AAAA,EACvB,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA,MACR,OAAO;AAAA;AAAA,MACP,QAAQ;AAAA,QACP,SAAS;AAAA;AAAA,QACT,MAAM;AAAA;AAAA,QACN,QAAQ;AAAA;AAAA,QACR,UAAU;AAAA;AAAA,QACV,MAAM;AAAA;AAAA,QACN,UAAU;AAAA;AAAA,QACV,UAAU;AAAA;AAAA,QACV,SAAS;AAAA;AAAA,QACT,SAAS;AAAA;AAAA,MACT;AAAA,MACD,WAAW,CAAE;AAAA;AAAA,IACd;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,WAAW;AAEV,UAAG,CAAC,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AACzD,eAAO;AAAA,MACR;AACA,UAAI,KAAK,UAAU,SAAS,MAAM;AACjC,YAAI,CAAC,SAAS,WAAW,WAAW,SAAS,EAAE,SAAS,KAAK,UAAU,IAAI,GAAG;AAC7E,iBAAOC,oDAAU,KAAK,UAAU,IAAI;AAAA,eAC9B;AACN,iBAAO;AAAA,QACR;AAAA,aACM;AACN,eAAO,KAAK,UAAU;AAAA,MACvB;AAAA,IACA;AAAA,IACD,eAAe;AACd,YAAM,QAAQ;AAAA,QACb,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,SAAS;AAAA,MACV;AAEA,YAAM,kBAAkB;AACxB,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,YAAM,QAAQ,CAAC;AAEf,YAAM,cAAc;AAOpB,aAAO;AAAA,IACP;AAAA,IACD,mBAAmB;AAClB,UAAI,WAAW;AACf,UAAI,CAAC,SAAS,WAAW,WAAW,SAAS,EAAE,SAAS,KAAK,UAAU,IAAI,GAAG;AAG7E,mBAAWC,kDAAQ,SAACC,wCAAK,MAAC,KAAK,UAAU,IAAI,CAAC;AAAA,MAC/C;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,eAAe;AACd,YAAM,eAAeC,0CAAa,cAAA,EAAG,cAAc,QAAQ,CAAC;AAC5D,UAAI,QAAQ;AAEZ,UAAG,KAAK,UAAU,aAAa,OAAO;AACrC,gBAAQ,CAAE,eAAe;AAAA,MAC1B,WAAU,KAAK,UAAU,aAAa,UAAU;AAC/C,gBAAQ,eAAe;AAAA,MACxB;AACA,YAAM,YAAY,cAAc,KAAK;AACrC,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,UAAU;AAET,KAAC,WAAW,WAAW,SAAS,WAAW,WAAW,SAAS,EAAE,IAAI,UAAQ;AAC5E,WAAK,IAAI,IAAI,aAAW,KAAK,KAAK;AAAA,QACjC,MAAM;AAAA,QACN;AAAA,OACA;AAAA,KACD;AAAA,EACD;AAAA,EACD,SAAS;AAAA;AAAA,IAER,KAAK,SAAS;AAEb,WAAK,YAAYC,0CAAAA,UAAU,KAAK,QAAQ,OAAO;AAE/C,WAAK,WAAW;AAChB,WAAK,SAAS;AAEd,UAAI,KAAK,UAAU,aAAa,IAAI;AACnC,aAAK,QAAQ,WAAW,MAAM;AAE7B,eAAK,WAAW;AAEhB,iBAAO,KAAK,UAAU,aAAc,cAAc,KAAK,UAAU,SAAS;AAAA,WACxE,KAAK,UAAU,QAAQ;AAAA,MAC3B;AAAA,IACA;AAAA;AAAA,IAED,OAAO;AACN,WAAK,WAAW;AAAA,IAChB;AAAA,IACD,aAAa;AACZ,WAAK,SAAS;AAEd,mBAAa,KAAK,KAAK;AACvB,WAAK,QAAQ;AAAA,IACd;AAAA,EACA;AAAA,EACD,gBAAgB;AACf,SAAK,WAAW;AAAA,EACjB;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/LD,GAAG,gBAAgB,SAAS;"}