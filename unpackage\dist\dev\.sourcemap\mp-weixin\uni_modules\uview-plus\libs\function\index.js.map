{"version": 3, "file": "index.js", "sources": ["uni_modules/uview-plus/libs/function/index.js"], "sourcesContent": ["import {\r\n\tnumber as testNumber,\r\n\tarray as testArray,\r\n\tempty as testEmpty\r\n} from './test'\r\nimport { round } from './digit.js'\r\nimport config from '../config/config';\r\n/**\r\n * @description 如果value小于min，取min；如果value大于max，取max\r\n * @param {number} min \r\n * @param {number} max \r\n * @param {number} value\r\n */\r\nexport function range(min = 0, max = 0, value = 0) {\r\n\treturn Math.max(min, Math.min(max, Number(value)))\r\n}\r\n\r\n/**\r\n * @description 用于获取用户传递值的px值  如果用户传递了\"xxpx\"或者\"xxrpx\"，取出其数值部分，如果是\"xxxrpx\"还需要用过uni.rpx2px进行转换\r\n * @param {number|string} value 用户传递值的px值\r\n * @param {boolean} unit \r\n * @returns {number|string}\r\n */\r\nexport function getPx(value, unit = false) {\r\n\tif (testNumber(value)) {\r\n\t\treturn unit ? `${value}px` : Number(value)\r\n\t}\r\n\t// 如果带有rpx，先取出其数值部分，再转为px值\r\n\tif (/(rpx|upx)$/.test(value)) {\r\n\t\treturn unit ? `${uni.upx2px(parseInt(value))}px` : Number(uni.upx2px(parseInt(value)))\r\n\t}\r\n\treturn unit ? `${parseInt(value)}px` : parseInt(value)\r\n}\r\n\r\n/**\r\n * @description 进行延时，以达到可以简写代码的目的 比如: await uni.$u.sleep(20)将会阻塞20ms\r\n * @param {number} value 堵塞时间 单位ms 毫秒\r\n * @returns {Promise} 返回promise\r\n */\r\nexport function sleep(value = 30) {\r\n\treturn new Promise((resolve) => {\r\n\t\tsetTimeout(() => {\r\n\t\t\tresolve()\r\n\t\t}, value)\r\n\t})\r\n}\r\n/**\r\n * @description 运行期判断平台\r\n * @returns {string} 返回所在平台(小写) \r\n * @link 运行期判断平台 https://uniapp.dcloud.io/frame?id=判断平台\r\n */\r\nexport function os() {\r\n\t// #ifdef APP || H5 || MP-WEIXIN\r\n\treturn uni.getDeviceInfo().platform.toLowerCase()\r\n\t// #endif\r\n\t// #ifndef APP || H5 || MP-WEIXIN\r\n\treturn uni.getSystemInfoSync().platform.toLowerCase()\r\n\t// #endif\r\n}\r\n/**\r\n * @description 获取系统信息同步接口\r\n * @link 获取系统信息同步接口 https://uniapp.dcloud.io/api/system/info?id=getsysteminfosync \r\n */\r\nexport function sys() {\r\n\treturn uni.getSystemInfoSync()\r\n}\r\nexport function getWindowInfo() {\r\n\tlet ret = {}\r\n\t// #ifdef APP || H5 || MP-WEIXIN\r\n\tret = uni.getWindowInfo()\r\n\t// #endif\r\n\t// #ifndef APP || H5 || MP-WEIXIN\r\n\tret = sys()\r\n\t// #endif\r\n\treturn ret\r\n}\r\nexport function getDeviceInfo() {\r\n\tlet ret = {}\r\n\t// #ifdef APP || H5 || MP-WEIXIN\r\n\tret = uni.getDeviceInfo()\r\n\t// #endif\r\n\t// #ifndef APP || H5 || MP-WEIXIN\r\n\tret = sys()\r\n\t// #endif\r\n\treturn ret\r\n}\r\n\r\n/**\r\n * @description 取一个区间数\r\n * @param {Number} min 最小值\r\n * @param {Number} max 最大值\r\n */\r\nexport function random(min, max) {\r\n\tif (min >= 0 && max > 0 && max >= min) {\r\n\t\tconst gab = max - min + 1\r\n\t\treturn Math.floor(Math.random() * gab + min)\r\n\t}\r\n\treturn 0\r\n}\r\n\r\n/**\r\n * @param {Number} len uuid的长度\r\n * @param {Boolean} firstU 将返回的首字母置为\"u\"\r\n * @param {Nubmer} radix 生成uuid的基数(意味着返回的字符串都是这个基数),2-二进制,8-八进制,10-十进制,16-十六进制\r\n */\r\nexport function guid(len = 32, firstU = true, radix = null) {\r\n\tconst chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')\r\n\tconst uuid = []\r\n\tradix = radix || chars.length\r\n\r\n\tif (len) {\r\n\t\t// 如果指定uuid长度,只是取随机的字符,0|x为位运算,能去掉x的小数位,返回整数位\r\n\t\tfor (let i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]\r\n\t} else {\r\n\t\tlet r\r\n\t\t// rfc4122标准要求返回的uuid中,某些位为固定的字符\r\n\t\tuuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'\r\n\t\tuuid[14] = '4'\r\n\r\n\t\tfor (let i = 0; i < 36; i++) {\r\n\t\t\tif (!uuid[i]) {\r\n\t\t\t\tr = 0 | Math.random() * 16\r\n\t\t\t\tuuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r]\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// 移除第一个字符,并用u替代,因为第一个字符为数值时,该guuid不能用作id或者class\r\n\tif (firstU) {\r\n\t\tuuid.shift()\r\n\t\treturn `u${uuid.join('')}`\r\n\t}\r\n\treturn uuid.join('')\r\n}\r\n\r\n/**\r\n* @description 获取父组件的参数，因为支付宝小程序不支持provide/inject的写法\r\n   this.$parent在非H5中，可以准确获取到父组件，但是在H5中，需要多次this.$parent.$parent.xxx\r\n   这里默认值等于undefined有它的含义，因为最顶层元素(组件)的$parent就是undefined，意味着不传name\r\n   值(默认为undefined)，就是查找最顶层的$parent\r\n*  @param {string|undefined} name 父组件的参数名\r\n*/\r\nexport function $parent(name = undefined) {\r\n\tlet parent = this.$parent\r\n\t// 通过while历遍，这里主要是为了H5需要多层解析的问题\r\n\twhile (parent) {\r\n\t\t// 父组件\r\n        name = name.replace(/up-([a-zA-Z0-9-_]+)/g, 'u-$1')        \r\n\t\tif (parent.$options && parent.$options.name !== name) {\r\n\t\t\t// 如果组件的name不相等，继续上一级寻找\r\n\t\t\tparent = parent.$parent\r\n\t\t} else {\r\n\t\t\treturn parent\r\n\t\t}\r\n\t}\r\n\treturn false\r\n}\r\n\r\n/**\r\n * @description 样式转换\r\n * 对象转字符串，或者字符串转对象\r\n * @param {object | string} customStyle 需要转换的目标\r\n * @param {String} target 转换的目的，object-转为对象，string-转为字符串\r\n * @returns {object|string}\r\n */\r\nexport function addStyle(customStyle, target = 'object') {\r\n\t// 字符串转字符串，对象转对象情形，直接返回\r\n\tif (testEmpty(customStyle) || typeof(customStyle) === 'object' && target === 'object' || target === 'string' &&\r\n\t\ttypeof(customStyle) === 'string') {\r\n\t\treturn customStyle\r\n\t}\r\n\t// 字符串转对象\r\n\tif (target === 'object') {\r\n\t\t// 去除字符串样式中的两端空格(中间的空格不能去掉，比如padding: 20px 0如果去掉了就错了)，空格是无用的\r\n\t\tcustomStyle = trim(customStyle)\r\n\t\t// 根据\";\"将字符串转为数组形式\r\n\t\tconst styleArray = customStyle.split(';')\r\n\t\tconst style = {}\r\n\t\t// 历遍数组，拼接成对象\r\n\t\tfor (let i = 0; i < styleArray.length; i++) {\r\n\t\t\t// 'font-size:20px;color:red;'，如此最后字符串有\";\"的话，会导致styleArray最后一个元素为空字符串，这里需要过滤\r\n\t\t\tif (styleArray[i]) {\r\n\t\t\t\tconst item = styleArray[i].split(':')\r\n\t\t\t\tstyle[trim(item[0])] = trim(item[1])\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn style\r\n\t}\r\n\t// 这里为对象转字符串形式\r\n\tlet string = ''\r\n\tif (typeof customStyle === 'object') {\r\n\t\tcustomStyle.forEach((val, i) => {\r\n\t\t\t// 驼峰转为中划线的形式，否则css内联样式，无法识别驼峰样式属性名\r\n\t\t\tconst key = i.replace(/([A-Z])/g, '-$1').toLowerCase()\r\n\t\t\tstring += `${key}:${val};`\r\n\t\t})\r\n\t}\r\n\t// 去除两端空格\r\n\treturn trim(string)\r\n}\r\n\r\n/**\r\n * @description 添加单位，如果有rpx，upx，%，px等单位结尾或者值为auto，直接返回，否则加上px单位结尾\r\n * @param {string|number} value 需要添加单位的值\r\n * @param {string} unit 添加的单位名 比如px\r\n */\r\nexport function addUnit(value = 'auto', unit = '') {\r\n\tif (!unit) {\r\n\t\tunit = config.unit || 'px'\r\n\t}\r\n\tif (unit == 'rpx' && testNumber(String(value))) {\r\n\t\tvalue = value * 2\r\n\t}\r\n\tvalue = String(value)\r\n\t// 用内置验证规则中的number判断是否为数值\r\n\treturn testNumber(value) ? `${value}${unit}` : value\r\n}\r\n\r\n/**\r\n * @description 深度克隆\r\n * @param {object} obj 需要深度克隆的对象\r\n * @returns {*} 克隆后的对象或者原值（不是对象）\r\n */\r\nexport function deepClone(obj) {\r\n\t// 对常见的“非”值，直接返回原来值\r\n\tif ([null, undefined, NaN, false].includes(obj)) return obj\r\n\tif (typeof obj !== 'object' && typeof obj !== 'function') {\r\n\t\t// 原始类型直接返回\r\n\t\treturn obj\r\n\t}\r\n\tconst o = testArray(obj) ? [] : {}\r\n\tfor (const i in obj) {\r\n\t\tif (obj.hasOwnProperty(i)) {\r\n\t\t\to[i] = typeof obj[i] === 'object' ? deepClone(obj[i]) : obj[i]\r\n\t\t}\r\n\t}\r\n\treturn o\r\n}\r\n\r\n/**\r\n * @description JS对象深度合并\r\n * @param {object} target 需要拷贝的对象\r\n * @param {object} source 拷贝的来源对象\r\n * @returns {object|boolean} 深度合并后的对象或者false（入参有不是对象）\r\n */\r\nexport function deepMerge(targetOrigin = {}, source = {}) {\r\n\tlet target = deepClone(targetOrigin)\r\n\tif (typeof target !== 'object' || typeof source !== 'object') return false\r\n\tfor (const prop in source) {\r\n\t\tif (!source.hasOwnProperty(prop)) continue\r\n\t\tif (prop in target) {\r\n\t\t\tif (source[prop] == null) {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t}else if (typeof target[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (typeof source[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (target[prop].concat && source[prop].concat) {\r\n\t\t\t\ttarget[prop] = target[prop].concat(source[prop])\r\n\t\t\t} else {\r\n\t\t\t\ttarget[prop] = deepMerge(target[prop], source[prop])\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttarget[prop] = source[prop]\r\n\t\t}\r\n\t}\r\n\treturn target\r\n}\r\n/**\r\n * @description JS对象深度合并\r\n * @param {object} target 需要拷贝的对象\r\n * @param {object} source 拷贝的来源对象\r\n * @returns {object|boolean} 深度合并后的对象或者false（入参有不是对象）\r\n */\r\nexport function shallowMerge(target, source = {}) {\r\n\tif (typeof target !== 'object' || typeof source !== 'object') return false\r\n\tfor (const prop in source) {\r\n\t\tif (!source.hasOwnProperty(prop)) continue\r\n\t\tif (prop in target) {\r\n\t\t\tif (source[prop] == null) {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t}else if (typeof target[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (typeof source[prop] !== 'object') {\r\n\t\t\t\ttarget[prop] = source[prop]\r\n\t\t\t} else if (target[prop].concat && source[prop].concat) {\r\n\t\t\t\ttarget[prop] = target[prop].concat(source[prop])\r\n\t\t\t} else {\r\n\t\t\t\ttarget[prop] = shallowMerge(target[prop], source[prop])\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttarget[prop] = source[prop]\r\n\t\t}\r\n\t}\r\n\treturn target\r\n}\r\n\r\n/**\r\n * @description error提示\r\n * @param {*} err 错误内容\r\n */\r\nexport function error(err) {\r\n\t// 开发环境才提示，生产环境不会提示\r\n\tif (process.env.NODE_ENV === 'development') {\r\n\t\tconsole.error(`uView提示：${err}`)\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 打乱数组\r\n * @param {array} array 需要打乱的数组\r\n * @returns {array} 打乱后的数组\r\n */\r\nexport function randomArray(array = []) {\r\n\t// 原理是sort排序,Math.random()产生0<= x < 1之间的数,会导致x-0.05大于或者小于0\r\n\treturn array.sort(() => Math.random() - 0.5)\r\n}\r\n\r\n// padStart 的 polyfill，因为某些机型或情况，还无法支持es7的padStart，比如电脑版的微信小程序\r\n// 所以这里做一个兼容polyfill的兼容处理\r\nif (!String.prototype.padStart) {\r\n\t// 为了方便表示这里 fillString 用了ES6 的默认参数，不影响理解\r\n\tString.prototype.padStart = function(maxLength, fillString = ' ') {\r\n\t\tif (Object.prototype.toString.call(fillString) !== '[object String]') {\r\n\t\t\tthrow new TypeError(\r\n\t\t\t\t'fillString must be String'\r\n\t\t\t)\r\n\t\t}\r\n\t\tconst str = this\r\n\t\t// 返回 String(str) 这里是为了使返回的值是字符串字面量，在控制台中更符合直觉\r\n\t\tif (str.length >= maxLength) return String(str)\r\n\r\n\t\tconst fillLength = maxLength - str.length\r\n\t\tlet times = Math.ceil(fillLength / fillString.length)\r\n\t\twhile (times >>= 1) {\r\n\t\t\tfillString += fillString\r\n\t\t\tif (times === 1) {\r\n\t\t\t\tfillString += fillString\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn fillString.slice(0, fillLength) + str\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 格式化时间\r\n * @param {String|Number} dateTime 需要格式化的时间戳\r\n * @param {String} fmt 格式化规则 yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合 默认yyyy-mm-dd\r\n * @returns {string} 返回格式化后的字符串\r\n */\r\nexport function timeFormat(dateTime = null, formatStr = 'yyyy-mm-dd') {\r\n  let date\r\n\t// 若传入时间为假值，则取当前时间\r\n  if (!dateTime) {\r\n    date = new Date()\r\n  }\r\n  // 若为unix秒时间戳，则转为毫秒时间戳（逻辑有点奇怪，但不敢改，以保证历史兼容）\r\n  else if (/^\\d{10}$/.test(dateTime.toString().trim())) {\r\n    date = new Date(dateTime * 1000)\r\n  }\r\n  // 若用户传入字符串格式时间戳，new Date无法解析，需做兼容\r\n  else if (typeof dateTime === 'string' && /^\\d+$/.test(dateTime.trim())) {\r\n    date = new Date(Number(dateTime))\r\n  }\r\n  // 其他都认为符合 RFC 2822 规范\r\n  else {\r\n    // 处理平台性差异，在Safari/Webkit中，new Date仅支持/作为分割符的字符串时间\r\n    date = new Date(\r\n      typeof dateTime === 'string'\r\n        ? dateTime.replace(/-/g, '/')\r\n        : dateTime\r\n    )\r\n  }\r\n\r\n\tconst timeSource = {\r\n\t\t'y': date.getFullYear().toString(), // 年\r\n\t\t'm': (date.getMonth() + 1).toString().padStart(2, '0'), // 月\r\n\t\t'd': date.getDate().toString().padStart(2, '0'), // 日\r\n\t\t'h': date.getHours().toString().padStart(2, '0'), // 时\r\n\t\t'M': date.getMinutes().toString().padStart(2, '0'), // 分\r\n\t\t's': date.getSeconds().toString().padStart(2, '0') // 秒\r\n\t\t// 有其他格式化字符需求可以继续添加，必须转化成字符串\r\n\t}\r\n\r\n  for (const key in timeSource) {\r\n    const [ret] = new RegExp(`${key}+`).exec(formatStr) || []\r\n    if (ret) {\r\n      // 年可能只需展示两位\r\n      const beginIndex = key === 'y' && ret.length === 2 ? 2 : 0\r\n      formatStr = formatStr.replace(ret, timeSource[key].slice(beginIndex))\r\n    }\r\n  }\r\n\r\n  return formatStr\r\n}\r\n\r\n/**\r\n * @description 时间戳转为多久之前\r\n * @param {String|Number} timestamp 时间戳\r\n * @param {String|Boolean} format \r\n * 格式化规则如果为时间格式字符串，超出一定时间范围，返回固定的时间格式；\r\n * 如果为布尔值false，无论什么时间，都返回多久以前的格式\r\n * @returns {string} 转化后的内容\r\n */\r\nexport function timeFrom(timestamp = null, format = 'yyyy-mm-dd') {\r\n\tif (timestamp == null) timestamp = Number(new Date())\r\n\ttimestamp = parseInt(timestamp)\r\n\t// 判断用户输入的时间戳是秒还是毫秒,一般前端js获取的时间戳是毫秒(13位),后端传过来的为秒(10位)\r\n\tif (timestamp.toString().length == 10) timestamp *= 1000\r\n\tlet timer = (new Date()).getTime() - timestamp\r\n\ttimer = parseInt(timer / 1000)\r\n\t// 如果小于5分钟,则返回\"刚刚\",其他以此类推\r\n\tlet tips = ''\r\n\tswitch (true) {\r\n\t\tcase timer < 300:\r\n\t\t\ttips = '刚刚'\r\n\t\t\tbreak\r\n\t\tcase timer >= 300 && timer < 3600:\r\n\t\t\ttips = `${parseInt(timer / 60)}分钟前`\r\n\t\t\tbreak\r\n\t\tcase timer >= 3600 && timer < 86400:\r\n\t\t\ttips = `${parseInt(timer / 3600)}小时前`\r\n\t\t\tbreak\r\n\t\tcase timer >= 86400 && timer < 2592000:\r\n\t\t\ttips = `${parseInt(timer / 86400)}天前`\r\n\t\t\tbreak\r\n\t\tdefault:\r\n\t\t\t// 如果format为false，则无论什么时间戳，都显示xx之前\r\n\t\t\tif (format === false) {\r\n\t\t\t\tif (timer >= 2592000 && timer < 365 * 86400) {\r\n\t\t\t\t\ttips = `${parseInt(timer / (86400 * 30))}个月前`\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttips = `${parseInt(timer / (86400 * 365))}年前`\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\ttips = timeFormat(timestamp, format)\r\n\t\t\t}\r\n\t}\r\n\treturn tips\r\n}\r\n\r\n/**\r\n * @description 去除空格\r\n * @param String str 需要去除空格的字符串\r\n * @param String pos both(左右)|left|right|all 默认both\r\n */\r\nexport function trim(str, pos = 'both') {\r\n\tstr = String(str)\r\n\tif (pos == 'both') {\r\n\t\treturn str.replace(/^\\s+|\\s+$/g, '')\r\n\t}\r\n\tif (pos == 'left') {\r\n\t\treturn str.replace(/^\\s*/, '')\r\n\t}\r\n\tif (pos == 'right') {\r\n\t\treturn str.replace(/(\\s*$)/g, '')\r\n\t}\r\n\tif (pos == 'all') {\r\n\t\treturn str.replace(/\\s+/g, '')\r\n\t}\r\n\treturn str\r\n}\r\n\r\n/**\r\n * @description 对象转url参数\r\n * @param {object} data,对象\r\n * @param {Boolean} isPrefix,是否自动加上\"?\"\r\n * @param {string} arrayFormat 规则 indices|brackets|repeat|comma\r\n */\r\nexport function queryParams(data = {}, isPrefix = true, arrayFormat = 'brackets') {\r\n\tconst prefix = isPrefix ? '?' : ''\r\n\tconst _result = []\r\n\tif (['indices', 'brackets', 'repeat', 'comma'].indexOf(arrayFormat) == -1) arrayFormat = 'brackets'\r\n\tfor (const key in data) {\r\n\t\tconst value = data[key]\r\n\t\t// 去掉为空的参数\r\n\t\tif (['', undefined, null].indexOf(value) >= 0) {\r\n\t\t\tcontinue\r\n\t\t}\r\n\t\t// 如果值为数组，另行处理\r\n\t\tif (value.constructor === Array) {\r\n\t\t\t// e.g. {ids: [1, 2, 3]}\r\n\t\t\tswitch (arrayFormat) {\r\n\t\t\t\tcase 'indices':\r\n\t\t\t\t\t// 结果: ids[0]=1&ids[1]=2&ids[2]=3\r\n\t\t\t\t\tfor (let i = 0; i < value.length; i++) {\r\n\t\t\t\t\t\t_result.push(`${key}[${i}]=${value[i]}`)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'brackets':\r\n\t\t\t\t\t// 结果: ids[]=1&ids[]=2&ids[]=3\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}[]=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'repeat':\r\n\t\t\t\t\t// 结果: ids=1&ids=2&ids=3\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'comma':\r\n\t\t\t\t\t// 结果: ids=1,2,3\r\n\t\t\t\t\tlet commaStr = ''\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\tcommaStr += (commaStr ? ',' : '') + _value\r\n\t\t\t\t\t})\r\n\t\t\t\t\t_result.push(`${key}=${commaStr}`)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tvalue.forEach((_value) => {\r\n\t\t\t\t\t\t_result.push(`${key}[]=${_value}`)\r\n\t\t\t\t\t})\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t_result.push(`${key}=${value}`)\r\n\t\t}\r\n\t}\r\n\treturn _result.length ? prefix + _result.join('&') : ''\r\n}\r\n\r\n/**\r\n * 显示消息提示框\r\n * @param {String} title 提示的内容，长度与 icon 取值有关。\r\n * @param {Number} duration 提示的延迟时间，单位毫秒，默认：2000\r\n */\r\nexport function toast(title, duration = 2000) {\r\n\tuni.showToast({\r\n\t\ttitle: String(title),\r\n\t\ticon: 'none',\r\n\t\tduration\r\n\t})\r\n}\r\n\r\n/**\r\n * @description 根据主题type值,获取对应的图标\r\n * @param {String} type 主题名称,primary|info|error|warning|success\r\n * @param {boolean} fill 是否使用fill填充实体的图标\r\n */\r\nexport function type2icon(type = 'success', fill = false) {\r\n\t// 如果非预置值,默认为success\r\n\tif (['primary', 'info', 'error', 'warning', 'success'].indexOf(type) == -1) type = 'success'\r\n\tlet iconName = ''\r\n\t// 目前(2019-12-12),info和primary使用同一个图标\r\n\tswitch (type) {\r\n\t\tcase 'primary':\r\n\t\t\ticonName = 'info-circle'\r\n\t\t\tbreak\r\n\t\tcase 'info':\r\n\t\t\ticonName = 'info-circle'\r\n\t\t\tbreak\r\n\t\tcase 'error':\r\n\t\t\ticonName = 'close-circle'\r\n\t\t\tbreak\r\n\t\tcase 'warning':\r\n\t\t\ticonName = 'error-circle'\r\n\t\t\tbreak\r\n\t\tcase 'success':\r\n\t\t\ticonName = 'checkmark-circle'\r\n\t\t\tbreak\r\n\t\tdefault:\r\n\t\t\ticonName = 'checkmark-circle'\r\n\t}\r\n\t// 是否是实体类型,加上-fill,在icon组件库中,实体的类名是后面加-fill的\r\n\tif (fill) iconName += '-fill'\r\n\treturn iconName\r\n}\r\n\r\n/**\r\n * @description 数字格式化\r\n * @param {number|string} number 要格式化的数字\r\n * @param {number} decimals 保留几位小数\r\n * @param {string} decimalPoint 小数点符号\r\n * @param {string} thousandsSeparator 千分位符号\r\n * @returns {string} 格式化后的数字\r\n */\r\nexport function priceFormat(number, decimals = 0, decimalPoint = '.', thousandsSeparator = ',') {\r\n\tnumber = (`${number}`).replace(/[^0-9+-Ee.]/g, '')\r\n\tconst n = !isFinite(+number) ? 0 : +number\r\n\tconst prec = !isFinite(+decimals) ? 0 : Math.abs(decimals)\r\n\tconst sep = (typeof thousandsSeparator === 'undefined') ? ',' : thousandsSeparator\r\n\tconst dec = (typeof decimalPoint === 'undefined') ? '.' : decimalPoint\r\n\tlet s = ''\r\n\r\n\ts = (prec ? round(n, prec) + '' : `${Math.round(n)}`).split('.')\r\n\tconst re = /(-?\\d+)(\\d{3})/\r\n\twhile (re.test(s[0])) {\r\n\t\ts[0] = s[0].replace(re, `$1${sep}$2`)\r\n\t}\r\n\t\r\n\tif ((s[1] || '').length < prec) {\r\n\t\ts[1] = s[1] || ''\r\n\t\ts[1] += new Array(prec - s[1].length + 1).join('0')\r\n\t}\r\n\treturn s.join(dec)\r\n}\r\n\r\n/**\r\n * @description 获取duration值\r\n * 如果带有ms或者s直接返回，如果大于一定值，认为是ms单位，小于一定值，认为是s单位\r\n * 比如以30位阈值，那么300大于30，可以理解为用户想要的是300ms，而不是想花300s去执行一个动画\r\n * @param {String|number} value 比如: \"1s\"|\"100ms\"|1|100\r\n * @param {boolean} unit  提示: 如果是false 默认返回number\r\n * @return {string|number} \r\n */\r\nexport function getDuration(value, unit = true) {\r\n\tconst valueNum = parseInt(value)\r\n\tif (unit) {\r\n\t\tif (/s$/.test(value)) return value\r\n\t\treturn value > 30 ? `${value}ms` : `${value}s`\r\n\t}\r\n\tif (/ms$/.test(value)) return valueNum\r\n\tif (/s$/.test(value)) return valueNum > 30 ? valueNum : valueNum * 1000\r\n\treturn valueNum\r\n}\r\n\r\n/**\r\n * @description 日期的月或日补零操作\r\n * @param {String} value 需要补零的值\r\n */\r\nexport function padZero(value) {\r\n\treturn `00${value}`.slice(-2)\r\n}\r\n\r\n/**\r\n * @description 在u-form的子组件内容发生变化，或者失去焦点时，尝试通知u-form执行校验方法\r\n * @param {*} instance\r\n * @param {*} event\r\n */\r\nexport function formValidate(instance, event) {\r\n\tconst formItem = $parent.call(instance, 'u-form-item')\r\n\tconst form = $parent.call(instance, 'u-form')\r\n\t// 如果发生变化的input或者textarea等，其父组件中有u-form-item或者u-form等，就执行form的validate方法\r\n\t// 同时将form-item的pros传递给form，让其进行精确对象验证\r\n\tif (formItem && form) {\r\n\t\tform.validateField(formItem.prop, () => {}, event)\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 获取某个对象下的属性，用于通过类似'a.b.c'的形式去获取一个对象的的属性的形式\r\n * @param {object} obj 对象\r\n * @param {string} key 需要获取的属性字段\r\n * @returns {*}\r\n */\r\nexport function getProperty(obj, key) {\r\n\tif (typeof obj !== 'object' || null == obj) {\r\n        return ''\r\n    }\r\n\tif (typeof key !== 'string' || key === '') {\r\n\t\treturn ''\r\n\t}\r\n\tif (key.indexOf('.') !== -1) {\r\n\t\tconst keys = key.split('.')\r\n\t\tlet firstObj = obj[keys[0]] || {}\r\n\r\n\t\tfor (let i = 1; i < keys.length; i++) {\r\n\t\t\tif (firstObj) {\r\n\t\t\t\tfirstObj = firstObj[keys[i]]\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn firstObj\r\n\t}\r\n\treturn obj[key]\r\n}\r\n\r\n/**\r\n * @description 设置对象的属性值，如果'a.b.c'的形式进行设置\r\n * @param {object} obj 对象\r\n * @param {string} key 需要设置的属性\r\n * @param {string} value 设置的值\r\n */\r\nexport function setProperty(obj, key, value) {\r\n\tif (typeof obj !== 'object' || null == obj) {\r\n\t\treturn\r\n\t}\r\n\t// 递归赋值\r\n\tconst inFn = function(_obj, keys, v) {\r\n\t\t// 最后一个属性key\r\n\t\tif (keys.length === 1) {\r\n\t\t\t_obj[keys[0]] = v\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// 0~length-1个key\r\n\t\twhile (keys.length > 1) {\r\n\t\t\tconst k = keys[0]\r\n\t\t\tif (!_obj[k] || (typeof _obj[k] !== 'object')) {\r\n\t\t\t\t_obj[k] = {}\r\n\t\t\t}\r\n\t\t\tconst key = keys.shift()\r\n\t\t\t// 自调用判断是否存在属性，不存在则自动创建对象\r\n\t\t\tinFn(_obj[k], keys, v)\r\n\t\t}\r\n\t}\r\n\r\n\tif (typeof key !== 'string' || key === '') {\r\n\r\n\t} else if (key.indexOf('.') !== -1) { // 支持多层级赋值操作\r\n\t\tconst keys = key.split('.')\r\n\t\tinFn(obj, keys, value)\r\n\t} else {\r\n\t\tobj[key] = value\r\n\t}\r\n}\r\n\r\n/**\r\n * @description 获取当前页面路径\r\n */\r\nexport function page() {\r\n\tconst pages = getCurrentPages()\r\n\t// 某些特殊情况下(比如页面进行redirectTo时的一些时机)，pages可能为空数组\r\n\treturn `/${pages[pages.length - 1].route || ''}`\r\n}\r\n\r\n/**\r\n * @description 获取当前路由栈实例数组\r\n */\r\nexport function pages() {\r\n\tconst pages = getCurrentPages()\r\n\treturn pages\r\n}\r\n\r\nexport function getValueByPath(obj, path) {\r\n    // 将路径字符串按 '.' 分割成数组\r\n    const pathArr = path.split('.');\r\n    // 使用 reduce 方法从 obj 开始，逐级访问嵌套属性\r\n    return pathArr.reduce((acc, curr) => {\r\n        // 如果当前累加器（acc）是对象且包含当前键（curr），则返回该键对应的值\r\n        // 否则返回 undefined（表示路径不存在）\r\n        return acc && acc[curr] !== undefined ? acc[curr] : undefined;\r\n    }, obj);\r\n}\r\n\r\n/**\r\n * 生成同色系浅色背景色\r\n * @param {string} textColor - 支持 #RGB、#RRGGBB、rgb()、rgba() 格式\r\n * @param {number} [lightness=85] - 目标亮度百分比（默认85%）\r\n * @returns {string} 十六进制颜色值\r\n */\r\nexport function genLightColor(textColor, lightness = 95) {\r\n\t// 手动解析颜色值（避免使用document）\r\n\tconst rgb = parseColorWithoutDOM(textColor);\r\n\t\r\n\t// RGB转HSL色域\r\n\tconst hsl = rgbToHsl(rgb.r, rgb.g, rgb.b);\r\n\t\r\n\t// 生成浅色背景\r\n\tconst bgHsl = {\r\n\t  h: hsl.h,\r\n\t  s: hsl.s,\r\n\t  l: Math.min(lightness, 95)\r\n\t};\r\n\t\r\n\treturn hslToHex(bgHsl.h, bgHsl.s, bgHsl.l);\r\n  }\r\n  \r\n  /* 手动解析颜色字符串（兼容uni-app环境） */\r\n  function parseColorWithoutDOM(colorStr) {\r\n\t// 统一转小写处理\r\n\tconst str = colorStr.toLowerCase().trim();\r\n\t\r\n\t// 处理十六进制格式\r\n\tif (str.startsWith('#')) {\r\n\t  const hex = str.replace('#', '');\r\n\t  const fullHex = hex.length === 3 ? \r\n\t\thex.split('').map(c => c + c).join('') : hex;\r\n\t\t\r\n\t  return {\r\n\t\tr: parseInt(fullHex.substring(0,2), 16),\r\n\t\tg: parseInt(fullHex.substring(2,4), 16),\r\n\t\tb: parseInt(fullHex.substring(4,6), 16)\r\n\t  };\r\n\t}\r\n\t\r\n\t// 处理rgb/rgba格式\r\n\tconst rgbMatch = str.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)/);\r\n\tif (rgbMatch) {\r\n\t  return {\r\n\t\tr: +rgbMatch[1],\r\n\t\tg: +rgbMatch[2],\r\n\t\tb: +rgbMatch[3]\r\n\t  };\r\n\t}\r\n\t\r\n\tthrow new Error('Invalid color format');\r\n  }\r\n\r\n// 辅助函数：RGB 转 HSL（色相、饱和度、亮度）\r\nfunction rgbToHsl(r, g, b) {\r\n r /= 255, g /= 255, b /= 255;\r\n const max = Math.max(r, g, b), min = Math.min(r, g, b);\r\n let h, s, l = (max + min) / 2;\r\n\r\n if (max === min) {\r\n   h = s = 0; // achromatic\r\n } else {\r\n   const d = max - min;\r\n   s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\r\n   switch (max) {\r\n\t case r: h = (g - b) / d + (g < b ? 6 : 0); break;\r\n\t case g: h = (b - r) / d + 2; break;\r\n\t case b: h = (r - g) / d + 4; break;\r\n   }\r\n   h = (h * 60).toFixed(1);\r\n }\r\n return { h: +h, s: +(s * 100).toFixed(1), l: +(l * 100).toFixed(1) };\r\n}\r\n\r\n// 辅助函数：HSL 转十六进制\r\nfunction hslToHex(h, s, l) {\r\n l /= 100;\r\n const a = s * Math.min(l, 1 - l) / 100;\r\n const f = n => {\r\n   const k = (n + h / 30) % 12;\r\n   const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\r\n   return Math.round(255 * color).toString(16).padStart(2, '0');\r\n };\r\n return `#${f(0)}${f(8)}${f(4)}`;\r\n}\r\n\r\nexport default {\r\n\trange,\r\n\tgetPx,\r\n\tsleep,\r\n\tos,\r\n\tsys,\r\n\tgetWindowInfo,\r\n\trandom,\r\n\tguid,\r\n\t$parent,\r\n\taddStyle,\r\n\taddUnit,\r\n\tdeepClone,\r\n\tdeepMerge,\r\n    shallowMerge,\r\n\terror,\r\n\trandomArray,\r\n\ttimeFormat,\r\n\ttimeFrom,\r\n\ttrim,\r\n\tqueryParams,\r\n\ttoast,\r\n\ttype2icon,\r\n\tpriceFormat,\r\n\tgetDuration,\r\n\tpadZero,\r\n\tformValidate,\r\n\tgetProperty,\r\n\tsetProperty,\r\n\tpage,\r\n\tpages,\r\n\tgetValueByPath,\r\n\tgenLightColor\r\n}\r\n"], "names": ["testNumber", "uni", "testEmpty", "config", "testArray", "round", "pages"], "mappings": ";;;;;AAaO,SAAS,MAAM,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG;AAC3C,SAAA,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC;AAClD;AAQgB,SAAA,MAAM,OAAO,OAAO,OAAO;AACtC,MAAAA,yCAAAA,OAAW,KAAK,GAAG;AACtB,WAAO,OAAO,GAAG,KAAK,OAAO,OAAO,KAAK;AAAA,EAC1C;AAEI,MAAA,aAAa,KAAK,KAAK,GAAG;AAC7B,WAAO,OAAO,GAAGC,cAAAA,MAAI,OAAO,SAAS,KAAK,CAAC,CAAC,OAAO,OAAOA,cAAAA,MAAI,OAAO,SAAS,KAAK,CAAC,CAAC;AAAA,EACtF;AACA,SAAO,OAAO,GAAG,SAAS,KAAK,CAAC,OAAO,SAAS,KAAK;AACtD;AAOgB,SAAA,MAAM,QAAQ,IAAI;AAC1B,SAAA,IAAI,QAAQ,CAAC,YAAY;AAC/B,eAAW,MAAM;AACR;OACN,KAAK;AAAA,EAAA,CACR;AACF;AAMO,SAAS,KAAK;AAEpB,SAAOA,cAAI,MAAA,cAAA,EAAgB,SAAS,YAAY;AAKjD;AAKO,SAAS,MAAM;AACrB,SAAOA,cAAAA,MAAI;AACZ;AACO,SAAS,gBAAgB;AAC/B,MAAI,MAAM,CAAA;AAEV,QAAMA,cAAAA,MAAI;AAKH,SAAA;AACR;AAiBgB,SAAA,OAAO,KAAK,KAAK;AAChC,MAAI,OAAO,KAAK,MAAM,KAAK,OAAO,KAAK;AAChC,UAAA,MAAM,MAAM,MAAM;AACxB,WAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,GAAG;AAAA,EAC5C;AACO,SAAA;AACR;AAOO,SAAS,KAAK,MAAM,IAAI,SAAS,MAAM,QAAQ,MAAM;AACrD,QAAA,QAAQ,iEAAiE,MAAM,EAAE;AACvF,QAAM,OAAO,CAAA;AACb,UAAQ,SAAS,MAAM;AAEvB,MAAI,KAAK;AAEC,aAAA,IAAI,GAAG,IAAI,KAAK;AAAK,WAAK,CAAC,IAAI,MAAM,IAAI,KAAK,OAAA,IAAW,KAAK;AAAA,EAAA,OACjE;AACF,QAAA;AAEC,SAAA,CAAC,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI;AAC3C,SAAK,EAAE,IAAI;AAEX,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACxB,UAAA,CAAC,KAAK,CAAC,GAAG;AACT,YAAA,IAAI,KAAK,OAAA,IAAW;AACnB,aAAA,CAAC,IAAI,MAAO,KAAK,KAAO,IAAI,IAAO,IAAM,CAAC;AAAA,MAChD;AAAA,IACD;AAAA,EACD;AAEA,MAAI,QAAQ;AACX,SAAK,MAAM;AACX,WAAO,IAAI,KAAK,KAAK,EAAE,CAAC;AAAA,EACzB;AACO,SAAA,KAAK,KAAK,EAAE;AACpB;AASgB,SAAA,QAAQ,OAAO,QAAW;AACzC,MAAI,SAAS,KAAK;AAElB,SAAO,QAAQ;AAED,WAAA,KAAK,QAAQ,wBAAwB,MAAM;AACxD,QAAI,OAAO,YAAY,OAAO,SAAS,SAAS,MAAM;AAErD,eAAS,OAAO;AAAA,IAAA,OACV;AACC,aAAA;AAAA,IACR;AAAA,EACD;AACO,SAAA;AACR;AASgB,SAAA,SAAS,aAAa,SAAS,UAAU;AAExD,MAAIC,+CAAU,WAAW,KAAK,OAAO,gBAAiB,YAAY,WAAW,YAAY,WAAW,YACnG,OAAO,gBAAiB,UAAU;AAC3B,WAAA;AAAA,EACR;AAEA,MAAI,WAAW,UAAU;AAExB,kBAAc,KAAK,WAAW;AAExB,UAAA,aAAa,YAAY,MAAM,GAAG;AACxC,UAAM,QAAQ,CAAA;AAEd,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAEvC,UAAA,WAAW,CAAC,GAAG;AAClB,cAAM,OAAO,WAAW,CAAC,EAAE,MAAM,GAAG;AAC9B,cAAA,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,MACpC;AAAA,IACD;AACO,WAAA;AAAA,EACR;AAEA,MAAI,SAAS;AACT,MAAA,OAAO,gBAAgB,UAAU;AACxB,gBAAA,QAAQ,CAAC,KAAK,MAAM;AAE/B,YAAM,MAAM,EAAE,QAAQ,YAAY,KAAK,EAAE;AAC/B,gBAAA,GAAG,GAAG,IAAI,GAAG;AAAA,IAAA,CACvB;AAAA,EACF;AAEA,SAAO,KAAK,MAAM;AACnB;AAOO,SAAS,QAAQ,QAAQ,QAAQ,OAAO,IAAI;AAClD,MAAI,CAAC,MAAM;AACV,WAAOC,yCAAAA,OAAO,QAAQ;AAAA,EACvB;AACA,MAAI,QAAQ,SAASH,yCAAAA,OAAW,OAAO,KAAK,CAAC,GAAG;AAC/C,YAAQ,QAAQ;AAAA,EACjB;AACA,UAAQ,OAAO,KAAK;AAEpB,SAAOA,gDAAW,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,KAAK;AAChD;AAOO,SAAS,UAAU,KAAK;AAE9B,MAAI,CAAC,MAAM,QAAW,KAAK,KAAK,EAAE,SAAS,GAAG;AAAU,WAAA;AACxD,MAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAElD,WAAA;AAAA,EACR;AACA,QAAM,IAAII,yCAAU,MAAA,GAAG,IAAI,CAAA,IAAK,CAAA;AAChC,aAAW,KAAK,KAAK;AAChB,QAAA,IAAI,eAAe,CAAC,GAAG;AAC1B,QAAE,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM,WAAW,UAAU,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC;AAAA,IAC9D;AAAA,EACD;AACO,SAAA;AACR;AAQO,SAAS,UAAU,eAAe,IAAI,SAAS,CAAA,GAAI;AACrD,MAAA,SAAS,UAAU,YAAY;AACnC,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAAiB,WAAA;AACrE,aAAW,QAAQ,QAAQ;AACtB,QAAA,CAAC,OAAO,eAAe,IAAI;AAAG;AAClC,QAAI,QAAQ,QAAQ;AACf,UAAA,OAAO,IAAI,KAAK,MAAM;AAClB,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MACjB,WAAA,OAAO,OAAO,IAAI,MAAM,UAAU;AACpC,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MAChB,WAAA,OAAO,OAAO,IAAI,MAAM,UAAU;AACrC,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MAAA,WAChB,OAAO,IAAI,EAAE,UAAU,OAAO,IAAI,EAAE,QAAQ;AAC/C,eAAA,IAAI,IAAI,OAAO,IAAI,EAAE,OAAO,OAAO,IAAI,CAAC;AAAA,MAAA,OACzC;AACC,eAAA,IAAI,IAAI,UAAU,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;AAAA,MACpD;AAAA,IAAA,OACM;AACC,aAAA,IAAI,IAAI,OAAO,IAAI;AAAA,IAC3B;AAAA,EACD;AACO,SAAA;AACR;AAOO,SAAS,aAAa,QAAQ,SAAS,IAAI;AACjD,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW;AAAiB,WAAA;AACrE,aAAW,QAAQ,QAAQ;AACtB,QAAA,CAAC,OAAO,eAAe,IAAI;AAAG;AAClC,QAAI,QAAQ,QAAQ;AACf,UAAA,OAAO,IAAI,KAAK,MAAM;AAClB,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MACjB,WAAA,OAAO,OAAO,IAAI,MAAM,UAAU;AACpC,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MAChB,WAAA,OAAO,OAAO,IAAI,MAAM,UAAU;AACrC,eAAA,IAAI,IAAI,OAAO,IAAI;AAAA,MAAA,WAChB,OAAO,IAAI,EAAE,UAAU,OAAO,IAAI,EAAE,QAAQ;AAC/C,eAAA,IAAI,IAAI,OAAO,IAAI,EAAE,OAAO,OAAO,IAAI,CAAC;AAAA,MAAA,OACzC;AACC,eAAA,IAAI,IAAI,aAAa,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC;AAAA,MACvD;AAAA,IAAA,OACM;AACC,aAAA,IAAI,IAAI,OAAO,IAAI;AAAA,IAC3B;AAAA,EACD;AACO,SAAA;AACR;AAMO,SAAS,MAAM,KAAK;AAEkB;AAC3CH,kBAAA,MAAc,MAAA,SAAA,wDAAA,WAAW,GAAG,EAAE;AAAA,EAC/B;AACD;AAOgB,SAAA,YAAY,QAAQ,IAAI;AAEvC,SAAO,MAAM,KAAK,MAAM,KAAK,OAAA,IAAW,GAAG;AAC5C;AAIA,IAAI,CAAC,OAAO,UAAU,UAAU;AAE/B,SAAO,UAAU,WAAW,SAAS,WAAW,aAAa,KAAK;AACjE,QAAI,OAAO,UAAU,SAAS,KAAK,UAAU,MAAM,mBAAmB;AACrE,YAAM,IAAI;AAAA,QACT;AAAA,MAAA;AAAA,IAEF;AACA,UAAM,MAAM;AAEZ,QAAI,IAAI,UAAU;AAAW,aAAO,OAAO,GAAG;AAExC,UAAA,aAAa,YAAY,IAAI;AACnC,QAAI,QAAQ,KAAK,KAAK,aAAa,WAAW,MAAM;AACpD,WAAO,UAAU,GAAG;AACL,oBAAA;AACd,UAAI,UAAU,GAAG;AACF,sBAAA;AAAA,MACf;AAAA,IACD;AACA,WAAO,WAAW,MAAM,GAAG,UAAU,IAAI;AAAA,EAAA;AAE3C;AAQO,SAAS,WAAW,WAAW,MAAM,YAAY,cAAc;AAChE,MAAA;AAEJ,MAAI,CAAC,UAAU;AACb,+BAAW;EAAK,WAGT,WAAW,KAAK,SAAS,SAAW,EAAA,KAAA,CAAM,GAAG;AAC7C,WAAA,IAAI,KAAK,WAAW,GAAI;AAAA,EAAA,WAGxB,OAAO,aAAa,YAAY,QAAQ,KAAK,SAAS,KAAK,CAAC,GAAG;AACtE,WAAO,IAAI,KAAK,OAAO,QAAQ,CAAC;AAAA,EAAA,OAG7B;AAEH,WAAO,IAAI;AAAA,MACT,OAAO,aAAa,WAChB,SAAS,QAAQ,MAAM,GAAG,IAC1B;AAAA,IAAA;AAAA,EAER;AAED,QAAM,aAAa;AAAA,IAClB,KAAK,KAAK,YAAY,EAAE,SAAS;AAAA;AAAA,IACjC,MAAM,KAAK,aAAa,GAAG,WAAW,SAAS,GAAG,GAAG;AAAA;AAAA,IACrD,KAAK,KAAK,QAAQ,EAAE,WAAW,SAAS,GAAG,GAAG;AAAA;AAAA,IAC9C,KAAK,KAAK,SAAS,EAAE,WAAW,SAAS,GAAG,GAAG;AAAA;AAAA,IAC/C,KAAK,KAAK,WAAW,EAAE,WAAW,SAAS,GAAG,GAAG;AAAA;AAAA,IACjD,KAAK,KAAK,WAAW,EAAE,WAAW,SAAS,GAAG,GAAG;AAAA;AAAA;AAAA,EAAA;AAIjD,aAAW,OAAO,YAAY;AAC5B,UAAM,CAAC,GAAG,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG,EAAE,KAAK,SAAS,KAAK,CAAA;AACvD,QAAI,KAAK;AAEP,YAAM,aAAa,QAAQ,OAAO,IAAI,WAAW,IAAI,IAAI;AAC7C,kBAAA,UAAU,QAAQ,KAAK,WAAW,GAAG,EAAE,MAAM,UAAU,CAAC;AAAA,IACtE;AAAA,EACF;AAEO,SAAA;AACT;AAUO,SAAS,SAAS,YAAY,MAAM,SAAS,cAAc;AACjE,MAAI,aAAa;AAAkB,gBAAA,OAAW,oBAAA,KAAA,CAAM;AACpD,cAAY,SAAS,SAAS;AAE1B,MAAA,UAAU,WAAW,UAAU;AAAiB,iBAAA;AACpD,MAAI,SAAS,oBAAI,KAAK,GAAG,YAAY;AAC7B,UAAA,SAAS,QAAQ,GAAI;AAE7B,MAAI,OAAO;AACX,UAAQ,MAAM;AAAA,IACb,KAAK,QAAQ;AACL,aAAA;AACP;AAAA,IACD,MAAK,SAAS,OAAO,QAAQ;AAC5B,aAAO,GAAG,SAAS,QAAQ,EAAE,CAAC;AAC9B;AAAA,IACD,MAAK,SAAS,QAAQ,QAAQ;AAC7B,aAAO,GAAG,SAAS,QAAQ,IAAI,CAAC;AAChC;AAAA,IACD,MAAK,SAAS,SAAS,QAAQ;AAC9B,aAAO,GAAG,SAAS,QAAQ,KAAK,CAAC;AACjC;AAAA,IACD;AAEC,UAAI,WAAW,OAAO;AACrB,YAAI,SAAS,UAAW,QAAQ,MAAM,OAAO;AAC5C,iBAAO,GAAG,SAAS,SAAS,QAAQ,GAAG,CAAC;AAAA,QAAA,OAClC;AACN,iBAAO,GAAG,SAAS,SAAS,QAAQ,IAAI,CAAC;AAAA,QAC1C;AAAA,MAAA,OACM;AACC,eAAA,WAAW,WAAW,MAAM;AAAA,MACpC;AAAA,EACF;AACO,SAAA;AACR;AAOgB,SAAA,KAAK,KAAK,MAAM,QAAQ;AACvC,QAAM,OAAO,GAAG;AAChB,MAAI,OAAO,QAAQ;AACX,WAAA,IAAI,QAAQ,cAAc,EAAE;AAAA,EACpC;AACA,MAAI,OAAO,QAAQ;AACX,WAAA,IAAI,QAAQ,QAAQ,EAAE;AAAA,EAC9B;AACA,MAAI,OAAO,SAAS;AACZ,WAAA,IAAI,QAAQ,WAAW,EAAE;AAAA,EACjC;AACA,MAAI,OAAO,OAAO;AACV,WAAA,IAAI,QAAQ,QAAQ,EAAE;AAAA,EAC9B;AACO,SAAA;AACR;AAQO,SAAS,YAAY,OAAO,IAAI,WAAW,MAAM,cAAc,YAAY;AAC3E,QAAA,SAAS,WAAW,MAAM;AAChC,QAAM,UAAU,CAAA;AACZ,MAAA,CAAC,WAAW,YAAY,UAAU,OAAO,EAAE,QAAQ,WAAW,KAAK;AAAkB,kBAAA;AACzF,aAAW,OAAO,MAAM;AACjB,UAAA,QAAQ,KAAK,GAAG;AAElB,QAAA,CAAC,IAAI,QAAW,IAAI,EAAE,QAAQ,KAAK,KAAK,GAAG;AAC9C;AAAA,IACD;AAEI,QAAA,MAAM,gBAAgB,OAAO;AAEhC,cAAQ,aAAa;AAAA,QACpB,KAAK;AAEJ,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC9B,oBAAA,KAAK,GAAG,GAAG,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE;AAAA,UACxC;AACA;AAAA,QACD,KAAK;AAEE,gBAAA,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,MAAM,MAAM,EAAE;AAAA,UAAA,CACjC;AACD;AAAA,QACD,KAAK;AAEE,gBAAA,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,IAAI,MAAM,EAAE;AAAA,UAAA,CAC/B;AACD;AAAA,QACD,KAAK;AAEJ,cAAI,WAAW;AACT,gBAAA,QAAQ,CAAC,WAAW;AACZ,yBAAA,WAAW,MAAM,MAAM;AAAA,UAAA,CACpC;AACD,kBAAQ,KAAK,GAAG,GAAG,IAAI,QAAQ,EAAE;AACjC;AAAA,QACD;AACO,gBAAA,QAAQ,CAAC,WAAW;AACzB,oBAAQ,KAAK,GAAG,GAAG,MAAM,MAAM,EAAE;AAAA,UAAA,CACjC;AAAA,MACH;AAAA,IAAA,OACM;AACN,cAAQ,KAAK,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,IAC/B;AAAA,EACD;AACA,SAAO,QAAQ,SAAS,SAAS,QAAQ,KAAK,GAAG,IAAI;AACtD;AAOgB,SAAA,MAAM,OAAO,WAAW,KAAM;AAC7CA,gBAAAA,MAAI,UAAU;AAAA,IACb,OAAO,OAAO,KAAK;AAAA,IACnB,MAAM;AAAA,IACN;AAAA,EAAA,CACA;AACF;AAOO,SAAS,UAAU,OAAO,WAAW,OAAO,OAAO;AAErD,MAAA,CAAC,WAAW,QAAQ,SAAS,WAAW,SAAS,EAAE,QAAQ,IAAI,KAAK;AAAW,WAAA;AACnF,MAAI,WAAW;AAEf,UAAQ,MAAM;AAAA,IACb,KAAK;AACO,iBAAA;AACX;AAAA,IACD,KAAK;AACO,iBAAA;AACX;AAAA,IACD,KAAK;AACO,iBAAA;AACX;AAAA,IACD,KAAK;AACO,iBAAA;AACX;AAAA,IACD,KAAK;AACO,iBAAA;AACX;AAAA,IACD;AACY,iBAAA;AAAA,EACb;AAEI,MAAA;AAAkB,gBAAA;AACf,SAAA;AACR;AAUO,SAAS,YAAY,QAAQ,WAAW,GAAG,eAAe,KAAK,qBAAqB,KAAK;AAC/F,WAAU,GAAG,MAAM,GAAI,QAAQ,gBAAgB,EAAE;AACjD,QAAM,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC;AAC9B,QAAA,OAAO,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,QAAQ;AACzD,QAAM,MAAO,OAAO,uBAAuB,cAAe,MAAM;AAChE,QAAM,MAAO,OAAO,iBAAiB,cAAe,MAAM;AAC1D,MAAI,IAAI;AAER,OAAK,OAAOI,0CAAAA,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,KAAK,MAAM,CAAC,CAAC,IAAI,MAAM,GAAG;AAC/D,QAAM,KAAK;AACX,SAAO,GAAG,KAAK,EAAE,CAAC,CAAC,GAAG;AACnB,MAAA,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,IAAI,KAAK,GAAG,IAAI;AAAA,EACrC;AAEA,OAAK,EAAE,CAAC,KAAK,IAAI,SAAS,MAAM;AAC/B,MAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACf,MAAE,CAAC,KAAK,IAAI,MAAM,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,GAAG;AAAA,EACnD;AACO,SAAA,EAAE,KAAK,GAAG;AAClB;AAUgB,SAAA,YAAY,OAAO,OAAO,MAAM;AACzC,QAAA,WAAW,SAAS,KAAK;AAC/B,MAAI,MAAM;AACL,QAAA,KAAK,KAAK,KAAK;AAAU,aAAA;AAC7B,WAAO,QAAQ,KAAK,GAAG,KAAK,OAAO,GAAG,KAAK;AAAA,EAC5C;AACI,MAAA,MAAM,KAAK,KAAK;AAAU,WAAA;AAC1B,MAAA,KAAK,KAAK,KAAK;AAAU,WAAA,WAAW,KAAK,WAAW,WAAW;AAC5D,SAAA;AACR;AAMO,SAAS,QAAQ,OAAO;AAC9B,SAAO,KAAK,KAAK,GAAG,MAAM,EAAE;AAC7B;AAOgB,SAAA,aAAa,UAAU,OAAO;AAC7C,QAAM,WAAW,QAAQ,KAAK,UAAU,aAAa;AACrD,QAAM,OAAO,QAAQ,KAAK,UAAU,QAAQ;AAG5C,MAAI,YAAY,MAAM;AAChB,SAAA,cAAc,SAAS,MAAM,MAAM;AAAA,OAAI,KAAK;AAAA,EAClD;AACD;AAQgB,SAAA,YAAY,KAAK,KAAK;AACrC,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK;AAC9B,WAAA;AAAA,EACX;AACH,MAAI,OAAO,QAAQ,YAAY,QAAQ,IAAI;AACnC,WAAA;AAAA,EACR;AACA,MAAI,IAAI,QAAQ,GAAG,MAAM,IAAI;AACtB,UAAA,OAAO,IAAI,MAAM,GAAG;AAC1B,QAAI,WAAW,IAAI,KAAK,CAAC,CAAC,KAAK,CAAA;AAE/B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,UAAI,UAAU;AACF,mBAAA,SAAS,KAAK,CAAC,CAAC;AAAA,MAC5B;AAAA,IACD;AACO,WAAA;AAAA,EACR;AACA,SAAO,IAAI,GAAG;AACf;AAQgB,SAAA,YAAY,KAAK,KAAK,OAAO;AAC5C,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAK;AAC3C;AAAA,EACD;AAEA,QAAM,OAAO,SAAS,MAAM,MAAM,GAAG;AAEhC,QAAA,KAAK,WAAW,GAAG;AACjB,WAAA,KAAK,CAAC,CAAC,IAAI;AAChB;AAAA,IACD;AAEO,WAAA,KAAK,SAAS,GAAG;AACjB,YAAA,IAAI,KAAK,CAAC;AACZ,UAAA,CAAC,KAAK,CAAC,KAAM,OAAO,KAAK,CAAC,MAAM,UAAW;AACzC,aAAA,CAAC,IAAI;MACX;AACY,WAAK,MAAM;AAEvB,WAAK,KAAK,CAAC,GAAG,MAAM,CAAC;AAAA,IACtB;AAAA,EAAA;AAGD,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAI;AAAA,WAEhC,IAAI,QAAQ,GAAG,MAAM,IAAI;AAC7B,UAAA,OAAO,IAAI,MAAM,GAAG;AACrB,SAAA,KAAK,MAAM,KAAK;AAAA,EAAA,OACf;AACN,QAAI,GAAG,IAAI;AAAA,EACZ;AACD;AAKO,SAAS,OAAO;AACtB,QAAMC,SAAQ;AAEd,SAAO,IAAIA,OAAMA,OAAM,SAAS,CAAC,EAAE,SAAS,EAAE;AAC/C;AAKO,SAAS,QAAQ;AACvB,QAAMA,SAAQ;AACPA,SAAAA;AACR;AAEgB,SAAA,eAAe,KAAK,MAAM;AAEhC,QAAA,UAAU,KAAK,MAAM,GAAG;AAE9B,SAAO,QAAQ,OAAO,CAAC,KAAK,SAAS;AAGjC,WAAO,OAAO,IAAI,IAAI,MAAM,SAAY,IAAI,IAAI,IAAI;AAAA,KACrD,GAAG;AACV;AAQgB,SAAA,cAAc,WAAW,YAAY,IAAI;AAElD,QAAA,MAAM,qBAAqB,SAAS;AAG1C,QAAM,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAGxC,QAAM,QAAQ;AAAA,IACZ,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,KAAK,IAAI,WAAW,EAAE;AAAA,EAAA;AAG3B,SAAO,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC;AAGA,SAAS,qBAAqB,UAAU;AAEzC,QAAM,MAAM,SAAS,YAAY,EAAE,KAAK;AAGpC,MAAA,IAAI,WAAW,GAAG,GAAG;AACvB,UAAM,MAAM,IAAI,QAAQ,KAAK,EAAE;AAC/B,UAAM,UAAU,IAAI,WAAW,IAChC,IAAI,MAAM,EAAE,EAAE,IAAI,OAAK,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI;AAEjC,WAAA;AAAA,MACR,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,MACtC,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,MACtC,GAAG,SAAS,QAAQ,UAAU,GAAE,CAAC,GAAG,EAAE;AAAA,IAAA;AAAA,EAEvC;AAGM,QAAA,WAAW,IAAI,MAAM,gCAAgC;AAC3D,MAAI,UAAU;AACL,WAAA;AAAA,MACR,GAAG,CAAC,SAAS,CAAC;AAAA,MACd,GAAG,CAAC,SAAS,CAAC;AAAA,MACd,GAAG,CAAC,SAAS,CAAC;AAAA,IAAA;AAAA,EAEf;AAEM,QAAA,IAAI,MAAM,sBAAsB;AACrC;AAGF,SAAS,SAAS,GAAG,GAAG,GAAG;AACrB,OAAA,KAAK,KAAK,KAAK,KAAK;AACzB,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GAAG,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACrD,MAAI,GAAG,GAAG,KAAK,MAAM,OAAO;AAE5B,MAAI,QAAQ,KAAK;AACf,QAAI,IAAI;AAAA,EAAA,OACH;AACL,UAAM,IAAI,MAAM;AAChB,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACd,KAAK;AAAG,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAAI;AAAA,MAC3C,KAAK;AAAQ,aAAA,IAAI,KAAK,IAAI;AAAG;AAAA,MAC7B,KAAK;AAAQ,aAAA,IAAI,KAAK,IAAI;AAAG;AAAA,IAC5B;AACK,SAAA,IAAI,IAAI,QAAQ,CAAC;AAAA,EACxB;AACA,SAAO,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC;AAClE;AAGA,SAAS,SAAS,GAAG,GAAG,GAAG;AACrB,OAAA;AACL,QAAM,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;AACnC,QAAM,IAAI,CAAK,MAAA;AACP,UAAA,KAAK,IAAI,IAAI,MAAM;AACzB,UAAM,QAAQ,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE;AACrD,WAAA,KAAK,MAAM,MAAM,KAAK,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAAA,EAAA;AAEtD,SAAA,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B;AAEA,MAAe,QAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACG;AAAA,EACH;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;;;;;;;;;;;;;;;;;;;;"}