{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-checkbox-group/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\n\nexport const props = defineMixin({\n    props: {\n        // 标识符\n        name: {\n            type: String,\n            default: () => defProps.checkboxGroup.name\n        },\n\t\t// #ifdef VUE3\n\t\t// 绑定的值\n\t\tmodelValue: {\n\t\t    type: Array,\n\t\t    default: () => defProps.checkboxGroup.value\n\t\t},\n\t\t// #endif\n\t\t// #ifdef VUE2\n\t\t// 绑定的值\n\t\tvalue: {\n\t\t    type: Array,\n\t\t    default: () => defProps.checkboxGroup.value\n\t\t},\n\t\t// #endif\n        // 形状，circle-圆形，square-方形\n        shape: {\n            type: String,\n            default: () => defProps.checkboxGroup.shape\n        },\n        // 是否禁用全部checkbox\n        disabled: {\n            type: Boolean,\n            default: () => defProps.checkboxGroup.disabled\n        },\n\n        // 选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值\n        activeColor: {\n            type: String,\n            default: () => defProps.checkboxGroup.activeColor\n        },\n        // 未选中的颜色\n        inactiveColor: {\n            type: String,\n            default: () => defProps.checkboxGroup.inactiveColor\n        },\n\n        // 整个组件的尺寸，默认px\n        size: {\n            type: [String, Number],\n            default: () => defProps.checkboxGroup.size\n        },\n        // 布局方式，row-横向，column-纵向\n        placement: {\n            type: String,\n            default: () => defProps.checkboxGroup.placement\n        },\n        // label的字体大小，px单位\n        labelSize: {\n            type: [String, Number],\n            default: () => defProps.checkboxGroup.labelSize\n        },\n        // label的字体颜色\n        labelColor: {\n            type: [String],\n            default: () => defProps.checkboxGroup.labelColor\n        },\n        // 是否禁止点击文本操作\n        labelDisabled: {\n            type: Boolean,\n            default: () => defProps.checkboxGroup.labelDisabled\n        },\n        // 图标颜色\n        iconColor: {\n            type: String,\n            default: () => defProps.checkboxGroup.iconColor\n        },\n        // 图标的大小，单位px\n        iconSize: {\n            type: [String, Number],\n            default: () => defProps.checkboxGroup.iconSize\n        },\n        // 勾选图标的对齐方式，left-左边，right-右边\n        iconPlacement: {\n            type: String,\n            default: () => defProps.checkboxGroup.iconPlacement\n        },\n        // 竖向配列时，是否显示下划线\n        borderBottom: {\n            type: Boolean,\n            default: () => defProps.checkboxGroup.borderBottom\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAGP,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAUK,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAGD,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAGD,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,MAAM;AAAA,MACb,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,cAAc;AAAA,IACzC;AAAA,EACJ;AACL,CAAC;;"}