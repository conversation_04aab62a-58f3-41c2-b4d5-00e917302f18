"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_input2 + _easycom_u_form_item2 + _easycom_u_icon2 + _easycom_u_form2 + _easycom_u_button2)();
}
const _easycom_u_input = () => "../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_u_form_item = () => "../../uni_modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_form = () => "../../uni_modules/uview-plus/components/u-form/u-form.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_input + _easycom_u_form_item + _easycom_u_icon + _easycom_u_form + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "register",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { updateProfile } = userStore;
    const formRef = common_vendor.ref();
    const isSubmitting = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      realName: "",
      phone: "",
      idCard: "",
      organization: "",
      position: "",
      avatar: ""
    });
    const formRules = common_vendor.reactive({
      realName: [
        { required: true, message: "请输入真实姓名", trigger: "blur" },
        { min: 2, max: 20, message: "姓名长度在2-20个字符", trigger: "blur" }
      ],
      phone: [
        { required: true, message: "请输入手机号码", trigger: "blur" },
        { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
      ],
      idCard: [
        { required: true, message: "请输入身份证号码", trigger: "blur" },
        { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号码", trigger: "blur" }
      ],
      organization: [
        { required: true, message: "请选择隶属机构", trigger: "change" }
      ],
      position: [
        { required: true, message: "请选择职位", trigger: "change" }
      ],
      avatar: [
        { required: true, message: "请上传本人照片", trigger: "change" }
      ]
    });
    function selectOrganization() {
      common_vendor.index.showActionSheet({
        itemList: ["市疾控中心", "区疾控中心", "县疾控中心", "其他机构"],
        success: (res) => {
          const organizations = ["市疾控中心", "区疾控中心", "县疾控中心", "其他机构"];
          formData.organization = organizations[res.tapIndex];
        }
      });
    }
    function selectPosition() {
      common_vendor.index.showActionSheet({
        itemList: ["主任医师", "副主任医师", "主治医师", "医师", "护师", "技师", "其他"],
        success: (res) => {
          const positions = ["主任医师", "副主任医师", "主治医师", "医师", "护师", "技师", "其他"];
          formData.position = positions[res.tapIndex];
        }
      });
    }
    function uploadAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: async (res) => {
          try {
            common_vendor.index.showLoading({ title: "上传中..." });
            formData.avatar = res.tempFilePaths[0];
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          } catch (error) {
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传失败，请重试",
              icon: "none"
            });
          }
        }
      });
    }
    async function submitForm() {
      try {
        await formRef.value.validate();
        isSubmitting.value = true;
        await src_api_modules_user.submitUserInfo(formData);
        updateProfile({ status: "pending" });
        common_vendor.index.showToast({
          title: "提交成功，请等待审核",
          icon: "success",
          duration: 2e3
        });
        setTimeout(() => {
          common_vendor.index.reLaunch({ url: "/pages/profile/profile" });
        }, 2e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/register/register.vue:255", "提交失败:", error);
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      } finally {
        isSubmitting.value = false;
      }
    }
    function skipRegister() {
      common_vendor.index.showModal({
        title: "确认跳过",
        content: "跳过后您将无法参加考试，只能进行学习练习，确认跳过吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.reLaunch({ url: "/pages/study/study" });
          }
        }
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => formData.realName = $event),
        b: common_vendor.p({
          placeholder: "请输入真实姓名",
          clearable: true,
          maxlength: "20",
          modelValue: formData.realName
        }),
        c: common_vendor.p({
          label: "真实姓名",
          prop: "realName",
          required: true
        }),
        d: common_vendor.o(($event) => formData.phone = $event),
        e: common_vendor.p({
          placeholder: "请输入手机号码",
          type: "number",
          clearable: true,
          maxlength: "11",
          modelValue: formData.phone
        }),
        f: common_vendor.p({
          label: "联系电话",
          prop: "phone",
          required: true
        }),
        g: common_vendor.o(($event) => formData.idCard = $event),
        h: common_vendor.p({
          placeholder: "请输入身份证号码",
          clearable: true,
          maxlength: "18",
          modelValue: formData.idCard
        }),
        i: common_vendor.p({
          label: "身份证号码",
          prop: "idCard",
          required: true
        }),
        j: common_vendor.o(selectOrganization),
        k: common_vendor.o(($event) => formData.organization = $event),
        l: common_vendor.p({
          placeholder: "请输入所属机构名称",
          clearable: true,
          readonly: true,
          modelValue: formData.organization
        }),
        m: common_vendor.p({
          label: "隶属机构",
          prop: "organization",
          required: true
        }),
        n: common_vendor.o(selectPosition),
        o: common_vendor.o(($event) => formData.position = $event),
        p: common_vendor.p({
          placeholder: "请输入职位",
          clearable: true,
          readonly: true,
          modelValue: formData.position
        }),
        q: common_vendor.p({
          label: "职位",
          prop: "position",
          required: true
        }),
        r: !formData.avatar
      }, !formData.avatar ? {
        s: common_vendor.p({
          name: "camera",
          size: "60",
          color: "#cccccc"
        }),
        t: common_vendor.o(uploadAvatar)
      } : {
        v: formData.avatar,
        w: common_vendor.p({
          name: "camera",
          size: "40",
          color: "#ffffff"
        }),
        x: common_vendor.o(uploadAvatar)
      }, {
        y: common_vendor.p({
          label: "本人照片",
          prop: "avatar",
          required: true
        }),
        z: common_vendor.sr(formRef, "bac4a35d-0", {
          "k": "formRef"
        }),
        A: common_vendor.p({
          model: formData,
          rules: formRules,
          labelPosition: "top",
          labelWidth: "auto"
        }),
        B: common_vendor.o(submitForm),
        C: common_vendor.p({
          type: "primary",
          loading: isSubmitting.value,
          loadingText: "提交中...",
          customStyle: "width: 100%; margin-bottom: 24rpx;"
        }),
        D: common_vendor.o(skipRegister),
        E: common_vendor.p({
          type: "info",
          plain: true,
          customStyle: "width: 100%;"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bac4a35d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/register/register.js.map
