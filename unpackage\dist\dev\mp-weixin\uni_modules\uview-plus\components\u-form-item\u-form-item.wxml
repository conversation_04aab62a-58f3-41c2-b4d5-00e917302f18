<view class="{{['u-form-item', 'data-v-42bac3de', s && 'u-form-item--error']}}"><view class="u-form-item__body data-v-42bac3de" bindtap="{{k}}" style="{{l + ';' + m}}"><block wx:if="{{$slots.label}}"><slot name="label"></slot></block><block wx:else><view wx:if="{{a}}" class="u-form-item__body__left data-v-42bac3de" style="{{'width:' + h + ';' + ('margin-bottom:' + i)}}"><view class="u-form-item__body__left__content data-v-42bac3de"><text wx:if="{{b}}" class="u-form-item__body__left__content__required data-v-42bac3de">*</text><view wx:if="{{c}}" class="u-form-item__body__left__content__icon data-v-42bac3de"><u-icon wx:if="{{d}}" class="data-v-42bac3de" u-i="42bac3de-0" bind:__l="__l" u-p="{{d}}"></u-icon></view><text class="u-form-item__body__left__content__label data-v-42bac3de" style="{{f + ';' + g}}">{{e}}</text></view></view></block><view class="u-form-item__body__right data-v-42bac3de"><view class="u-form-item__body__right__content data-v-42bac3de"><view class="u-form-item__body__right__content__slot data-v-42bac3de"><slot/></view><view wx:if="{{j}}" class="item__body__right__content__icon data-v-42bac3de"><slot name="right"/></view></view></view></view><block wx:if="{{$slots.error}}"><slot name="error"></slot></block><block wx:else><text wx:if="{{n}}" class="u-form-item__body__right__message data-v-42bac3de" style="{{'margin-left:' + p}}">{{o}}</text></block><u-line wx:if="{{q}}" class="data-v-42bac3de" u-i="42bac3de-1" bind:__l="__l" u-p="{{r}}"></u-line></view>