{"version": 3, "file": "value.js", "sources": ["uni_modules/uview-plus/components/u-text/value.js"], "sourcesContent": ["import { error, priceFormat, timeFormat } from '../../libs/function/index';\nimport test from '../../libs/function/test';\nexport default {\n    computed: {\n        // 经处理后需要显示的值\n        value() {\n            const {\n                text,\n                mode,\n                format,\n                href\n            } = this\n            // 价格类型\n            if (mode === 'price') {\n                // 如果text不为金额进行提示\n                if (!/^\\d+(\\.\\d+)?$/.test(text)) {\n                    error('金额模式下，text参数需要为金额格式');\n                }\n                // 进行格式化，判断用户传入的format参数为正则，或者函数，如果没有传入format，则使用默认的金额格式化处理\n                if (test.func(format)) {\n                    // 如果用户传入的是函数，使用函数格式化\n                    return format(text)\n                }\n                // 如果format非正则，非函数，则使用默认的金额格式化方法进行操作\n                return priceFormat(text, 2)\n            } if (mode === 'date') {\n                // 判断是否合法的日期或者时间戳\n                !test.date(text) && error('日期模式下，text参数需要为日期或时间戳格式')\n                // 进行格式化，判断用户传入的format参数为正则，或者函数，如果没有传入format，则使用默认的格式化处理\n                if (test.func(format)) {\n                    // 如果用户传入的是函数，使用函数格式化\n                    return format(text)\n                } if (format) {\n                    // 如果format非正则，非函数，则使用默认的时间格式化方法进行操作\n                    return timeFormat(text, format)\n                }\n                // 如果没有设置format，则设置为默认的时间格式化形式\n                return timeFormat(text, 'yyyy-mm-dd')\n            } if (mode === 'phone') {\n                // 判断是否合法的手机号\n                // !test.mobile(text) && error('手机号模式下，text参数需要为手机号码格式')\n                if (test.func(format)) {\n                    // 如果用户传入的是函数，使用函数格式化\n                    return format(text)\n                } if (format === 'encrypt') {\n                    // 如果format为encrypt，则将手机号进行星号加密处理\n                    return `${text.substr(0, 3)}****${text.substr(7)}`\n                }\n                return text\n            } if (mode === 'name') {\n                // 判断是否合法的字符粗\n                !(typeof (text) === 'string') && error('姓名模式下，text参数需要为字符串格式')\n                if (test.func(format)) {\n                    // 如果用户传入的是函数，使用函数格式化\n                    return format(text)\n                } if (format === 'encrypt') {\n                    // 如果format为encrypt，则将姓名进行星号加密处理\n                    return this.formatName(text)\n                }\n                return text\n            } if (mode === 'link') {\n                // 判断是否合法的字符粗\n                !test.url(href) && error('超链接模式下，href参数需要为URL格式')\n                return text\n            }\n            return text\n        }\n    },\n    methods: {\n        // 默认的姓名脱敏规则\n        formatName(name) {\n            let value = ''\n            if (name.length === 2) {\n                value = name.substr(0, 1) + '*'\n            } else if (name.length > 2) {\n                let char = ''\n                for (let i = 0, len = name.length - 2; i < len; i++) {\n                    char += '*'\n                }\n                value = name.substr(0, 1) + char + name.substr(-1, 1)\n            } else {\n                value = name\n            }\n            return value\n        }\n    }\n}\n"], "names": ["error", "test", "priceFormat", "timeFormat", "value"], "mappings": ";;;AAEA,MAAe,QAAA;AAAA,EACX,UAAU;AAAA;AAAA,IAEN,QAAQ;AACJ,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAChB,IAAgB;AAEJ,UAAI,SAAS,SAAS;AAElB,YAAI,CAAC,gBAAgB,KAAK,IAAI,GAAG;AAC7BA,oDAAK,MAAC,qBAAqB;AAAA,QAC9B;AAED,YAAIC,yCAAI,KAAC,KAAK,MAAM,GAAG;AAEnB,iBAAO,OAAO,IAAI;AAAA,QACrB;AAED,eAAOC,0CAAW,YAAC,MAAM,CAAC;AAAA,MAC1C;AAAc,UAAI,SAAS,QAAQ;AAEnB,SAACD,yCAAI,KAAC,KAAK,IAAI,KAAKD,0CAAAA,MAAM,yBAAyB;AAEnD,YAAIC,yCAAI,KAAC,KAAK,MAAM,GAAG;AAEnB,iBAAO,OAAO,IAAI;AAAA,QACrB;AAAC,YAAI,QAAQ;AAEV,iBAAOE,0CAAU,WAAC,MAAM,MAAM;AAAA,QACjC;AAED,eAAOA,0CAAU,WAAC,MAAM,YAAY;AAAA,MACpD;AAAc,UAAI,SAAS,SAAS;AAGpB,YAAIF,yCAAI,KAAC,KAAK,MAAM,GAAG;AAEnB,iBAAO,OAAO,IAAI;AAAA,QACtC;AAAkB,YAAI,WAAW,WAAW;AAExB,iBAAO,GAAG,KAAK,OAAO,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,QACnD;AACD,eAAO;AAAA,MACvB;AAAc,UAAI,SAAS,QAAQ;AAEnB,UAAE,OAAQ,SAAU,aAAaD,0CAAAA,MAAM,sBAAsB;AAC7D,YAAIC,yCAAI,KAAC,KAAK,MAAM,GAAG;AAEnB,iBAAO,OAAO,IAAI;AAAA,QACtC;AAAkB,YAAI,WAAW,WAAW;AAExB,iBAAO,KAAK,WAAW,IAAI;AAAA,QAC9B;AACD,eAAO;AAAA,MACvB;AAAc,UAAI,SAAS,QAAQ;AAEnB,SAACA,yCAAI,KAAC,IAAI,IAAI,KAAKD,0CAAAA,MAAM,uBAAuB;AAChD,eAAO;AAAA,MACV;AACD,aAAO;AAAA,IACV;AAAA,EACJ;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,WAAW,MAAM;AACb,UAAII,SAAQ;AACZ,UAAI,KAAK,WAAW,GAAG;AACnB,QAAAA,SAAQ,KAAK,OAAO,GAAG,CAAC,IAAI;AAAA,MAC5C,WAAuB,KAAK,SAAS,GAAG;AACxB,YAAI,OAAO;AACX,iBAAS,IAAI,GAAG,MAAM,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK;AACjD,kBAAQ;AAAA,QACX;AACD,QAAAA,SAAQ,KAAK,OAAO,GAAG,CAAC,IAAI,OAAO,KAAK,OAAO,IAAI,CAAC;AAAA,MACpE,OAAmB;AACH,QAAAA,SAAQ;AAAA,MACX;AACD,aAAOA;AAAA,IACV;AAAA,EACJ;AACL;;"}