# Login页面UI优化任务记录

**创建时间**: 2025-01-27T10:30:00  
**任务状态**: 已完成  
**问题描述**: login.vue页面存在字体重叠、字体大小不对、背景图丑陋等UI问题  

## 问题分析

### 核心问题
1. **字体重叠**: uview-plus的u-text组件lineHeight参数被误用为倍数，实际应传入具体rpx数值
2. **字体大小**: 在不同平台下rpx转换导致字体显示偏小
3. **背景配色**: 渐变色对比度过高，光斑效果过亮，缺乏层次感

### 技术原因
- `u-text`组件内部使用`addUnit(this.lineHeight)`直接设置CSS
- `:lineHeight="1.3"`实际渲染为`line-height: 1.3rpx`，远小于字体高度
- 背景色`#2196f3`到`#64b5f6`亮度过高，光斑透明度0.12/0.08偏亮

## 解决方案

### 方案选择: 系统化设计变量 + 计算行高

### 具体实施

#### 1. 行高修复 (pages/login/login.vue)
```typescript
// 新增计算属性
const headlineLineHeight = computed(() => Math.round(headlineFontSize.value * 1.3))
const sublineLineHeight = computed(() => Math.round(sublineFontSize.value * 1.4))
const agreementLineHeight = computed(() => Math.round(agreementFontSize.value * 1.5))
const hintLineHeight = computed(() => Math.round(hintFontSize.value * 1.4))
const modalTextLineHeight = computed(() => Math.round(modalTextSize.value * 1.6))
```

#### 2. 模板绑定更新
- 替换所有`:lineHeight="1.x"`为对应计算属性
- 确保传入具体rpx数值而非倍数

#### 3. 排版系统完善 (src/styles/variables.scss)
```scss
// 行高倍数系统
$line-height-tight: 1.2;
$line-height-normal: 1.4;
$line-height-loose: 1.6;

// 具体行高值（用于uview-plus组件）
$line-height-xs: 28rpx;   // 20*1.4
$line-height-sm: 34rpx;   // 24*1.4
$line-height-md: 39rpx;   // 28*1.4
$line-height-lg: 45rpx;   // 32*1.4
$line-height-xl: 50rpx;   // 36*1.4
$line-height-xxl: 56rpx;  // 40*1.4
```

#### 4. 背景配色优化
```scss
$login-bg-main: #1e88e5;     // 降低明度
$login-bg-light: #4a90e2;    // 更柔和的蓝色
$login-bg-glow1: rgba(255,255,255,0.08);  // 降低透明度
$login-bg-glow2: rgba(255,255,255,0.05);
```

#### 5. 视觉层次增强
- 添加深色蒙版`rgba(0,0,0,0.1)`提升对比度
- 调整z-index确保内容显示在蒙版之上
- 增强协议区域背景透明度和边框

## 修改文件清单

1. **pages/login/login.vue**
   - 新增6个行高计算属性
   - 更新模板中所有lineHeight绑定
   - 优化样式层次和视觉效果

2. **src/styles/variables.scss**
   - 添加完整行高变量系统
   - 优化登录页背景配色变量

## 预期效果

✅ **字体重叠问题解决**: 行高计算准确，文字显示正常  
✅ **字体大小合适**: 响应式字体系统适配不同平台  
✅ **背景美观**: 柔和渐变+适度光斑+层次蒙版  
✅ **系统化**: 可复用的排版变量，便于后续页面使用  

## 技术要点

- uview-plus的lineHeight需传入具体数值而非倍数
- 使用Math.round()确保行高为整数rpx
- 通过z-index和蒙版控制视觉层次
- 变量系统便于主题切换和全局调整 