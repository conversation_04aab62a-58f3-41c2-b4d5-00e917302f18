{"version": 3, "file": "u-modal.js", "sources": ["uni_modules/uview-plus/components/u-modal/u-modal.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1tb2RhbC91LW1vZGFsLnZ1ZQ"], "sourcesContent": ["<template>\n\t<u-popup\n\t\tmode=\"center\"\n\t\t:zoom=\"zoom\"\n\t\t:show=\"show\"\n\t\t:class=\"[customClass]\"\n\t\t:customStyle=\"{\n\t\t\tborderRadius: '6px', \n\t\t\toverflow: 'hidden',\n\t\t\tmarginTop: `-${addUnit(negativeTop)}`\n\t\t}\"\n\t\t:closeOnClickOverlay=\"closeOnClickOverlay\"\n\t\t:safeAreaInsetBottom=\"false\"\n\t\t:duration=\"duration\"\n\t\t@click=\"clickHandler\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-modal\"\n\t\t\t:style=\"{\n\t\t\t\twidth: addUnit(width),\n\t\t\t}\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-modal__title\"\n\t\t\t\tv-if=\"title\"\n\t\t\t>{{ title }}</view>\n\t\t\t<view\n\t\t\t\tclass=\"u-modal__content\"\n\t\t\t\t:style=\"contentStyleCpu\"\n\t\t\t>\n\t\t\t\t<slot>\n\t\t\t\t\t<text class=\"u-modal__content__text\" :style=\"{textAlign: contentTextAlign}\">\n\t\t\t\t\t\t{{ content }}\n\t\t\t\t\t</text>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tclass=\"u-modal__button-group--confirm-button\"\n\t\t\t\tv-if=\"$slots.confirmButton\"\n\t\t\t>\n\t\t\t\t<slot name=\"confirmButton\"></slot>\n\t\t\t</view>\n\t\t\t<template v-else>\n\t\t\t\t<u-line></u-line>\n\t\t\t\t<view\n\t\t\t\t\tclass=\"u-modal__button-group\"\n\t\t\t\t\t:style=\"{\n\t\t\t\t\t\tflexDirection: buttonReverse ? 'row-reverse' : 'row'\n\t\t\t\t\t}\"\n\t\t\t\t>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-modal__button-group__wrapper u-modal__button-group__wrapper--cancel\"\n\t\t\t\t\t\t:hover-stay-time=\"150\"\n\t\t\t\t\t\thover-class=\"u-modal__button-group__wrapper--hover\"\n\t\t\t\t\t\t:class=\"[showCancelButton && !showConfirmButton && 'u-modal__button-group__wrapper--only-cancel']\"\n\t\t\t\t\t\tv-if=\"showCancelButton\"\n\t\t\t\t\t\t@tap=\"cancelHandler\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t\tclass=\"u-modal__button-group__wrapper__text\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\tcolor: cancelColor\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t>{{ cancelText }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<u-line\n\t\t\t\t\t\tdirection=\"column\"\n\t\t\t\t\t\tv-if=\"showConfirmButton && showCancelButton\"\n\t\t\t\t\t></u-line>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"u-modal__button-group__wrapper u-modal__button-group__wrapper--confirm\"\n\t\t\t\t\t\t:hover-stay-time=\"150\"\n\t\t\t\t\t\thover-class=\"u-modal__button-group__wrapper--hover\"\n\t\t\t\t\t\t:class=\"[!showCancelButton && showConfirmButton && 'u-modal__button-group__wrapper--only-confirm']\"\n\t\t\t\t\t\tv-if=\"showConfirmButton\"\n\t\t\t\t\t\t@tap=\"confirmHandler\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<u-loading-icon v-if=\"loading\"></u-loading-icon>\n\t\t\t\t\t\t<text\n\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\tclass=\"u-modal__button-group__wrapper__text\"\n\t\t\t\t\t\t\t:style=\"{\n\t\t\t\t\t\t\t\tcolor: confirmColor\n\t\t\t\t\t\t\t}\"\n\t\t\t\t\t\t>{{ confirmText }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</view>\n\t\t<template #bottom>\n\t\t\t<slot name=\"popupBottom\"></slot>\n\t\t</template>\n\t</u-popup>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit } from '../../libs/function/index';\n\t/**\n\t * Modal 模态框\n\t * @description 弹出模态框，常用于消息提示、消息确认、在当前页面内完成特定的交互操作。\n\t * @tutorial https://ijry.github.io/uview-plus/components/modul.html\n\t * @property {Boolean}\t\t\tshow\t\t\t\t是否显示模态框，请赋值给show （默认 false ）\n\t * @property {String}\t\t\ttitle\t\t\t\t标题内容\n\t * @property {String}\t\t\tcontent\t\t\t\t模态框内容，如传入slot内容，则此参数无效\n\t * @property {String}\t\t\tconfirmText\t\t\t确认按钮的文字 （默认 '确认' ）\n\t * @property {String}\t\t\tcancelText\t\t\t取消按钮的文字 （默认 '取消' ）\n\t * @property {Boolean}\t\t\tshowConfirmButton\t是否显示确认按钮 （默认 true ）\n\t * @property {Boolean}\t\t\tshowCancelButton\t是否显示取消按钮 （默认 false ）\n\t * @property {String}\t\t\tconfirmColor\t\t确认按钮的颜色 （默认 '#2979ff' ）\n\t * @property {String}\t\t\tcancelColor\t\t\t取消按钮的颜色 （默认 '#606266' ）\n\t * @property {Boolean}\t\t\tbuttonReverse\t\t对调确认和取消的位置 （默认 false ）\n\t * @property {Boolean}\t\t\tzoom\t\t\t\t是否开启缩放模式 （默认 true ）\n\t * @property {Boolean}\t\t\tasyncClose\t\t\t是否异步关闭，只对确定按钮有效，见上方说明 （默认 false ）\n\t * @property {Boolean}\t\t\tcloseOnClickOverlay\t是否允许点击遮罩关闭Modal （默认 false ）\n\t * @property {String | Number}\tnegativeTop\t\t\t往上偏移的值，给一个负的margin-top，往上偏移，避免和键盘重合的情况，单位任意，数值则默认为px单位 （默认 0 ）\n\t * @property {String | Number}\twidth\t\t\t\tmodal宽度，不支持百分比，可以数值，px，rpx单位 （默认 '650rpx' ）\n\t * @property {String}\t\t\tconfirmButtonShape\t确认按钮的样式,如设置，将不会显示取消按钮\n\t * @property {Number}\t\t\tduration\t\t\t弹窗动画过度时间 （默认 400 ）\n\t * @event {Function} confirm\t点击确认按钮时触发\n\t * @event {Function} cancel\t\t点击取消按钮时触发\n\t * @event {Function} close\t\t点击遮罩关闭出发，closeOnClickOverlay为true有效\n\t * @example <u-modal :show=\"show\" />\n\t */\n\texport default {\n\t\tname: 'u-modal',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tloading: false\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tshow(n) {\n\t\t\t\t// 为了避免第一次打开modal，又使用了异步关闭的loading\n\t\t\t\t// 第二次打开modal时，loading依然存在的情况\n\t\t\t\tif (n && this.loading) this.loading = false\n\t\t\t}\n\t\t},\n\t\temits: [\"confirm\", \"cancel\", \"close\", \"update:show\", 'cancelOnAsync'],\n\t\tcomputed: {\n\t\t\tcontentStyleCpu() {\n\t\t\t\tlet style = this.contentStyle;\n\t\t\t\tstyle.paddingTop = `${this.title ? 12 : 25}px`\n\t\t\t\treturn style;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddUnit,\n\t\t\t// 点击确定按钮\n\t\t\tconfirmHandler() {\n\t\t\t\t// 如果配置了异步关闭，将按钮值为loading状态\n\t\t\t\tif (this.asyncClose) {\n\t\t\t\t\tthis.loading = true;\n\t\t\t\t} else {\n\t\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\t}\n\t\t\t\tthis.$emit('confirm')\n\t\t\t},\n\t\t\t// 点击取消按钮\n\t\t\tcancelHandler() {\n\t\t\t\t// 如果点击了确定按钮，确定按钮正在请求接口执行异步操作，那么限制不能取消。\n\t\t\t\tif (this.asyncClose && this.loading) {\n\t\t\t\t\tif (this.asyncCloseTip) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: this.asyncCloseTip,\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\tthis.$emit('cancelOnAsync')\n\t\t\t\t} else {\n\t\t\t\t\t// 如果配置了取消时异步关闭\n\t\t\t\t\tif (!this.asyncCancelClose) {\n\t\t\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\t\t\t// 点击遮罩\n\t\t\t// 从原理上来说，modal的遮罩点击，并不是真的点击到了遮罩\n\t\t\t// 因为modal依赖于popup的中部弹窗类型，中部弹窗比较特殊，虽有然遮罩，但是为了让弹窗内容能flex居中\n\t\t\t// 多了一个透明的遮罩，此透明的遮罩会覆盖在灰色的遮罩上，所以实际上是点击不到灰色遮罩的，popup内部在\n\t\t\t// 透明遮罩的子元素做了.stop处理，所以点击内容区，也不会导致误触发\n\t\t\tclickHandler() {\n\t\t\t\tif (this.closeOnClickOverlay) {\n\t\t\t\t\tthis.$emit('update:show', false)\n\t\t\t\t\tthis.$emit('close')\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t$u-modal-border-radius: 6px;\n\n\t.u-modal {\n\t\twidth: 650rpx;\n\t\tborder-radius: $u-modal-border-radius;\n\t\toverflow: hidden;\n\n\t\t&__title {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tfont-size: 16px;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: $u-content-color;\n\t\t\ttext-align: center;\n\t\t\tpadding-top: 25px;\n\t\t}\n\n\t\t&__content {\n\t\t\tpadding: 12px 25px 25px 25px;\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\n\t\t\t&__text {\n\t\t\t\tfont-size: 15px;\n\t\t\t\tcolor: $u-content-color;\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\n\t\t&__button-group {\n\t\t\t@include flex;\n\n\t\t\t&--confirm-button {\n\t\t\t\tflex-direction: column;\n\t\t\t\tpadding: 0px 25px 15px 25px;\n\t\t\t}\n\n\t\t\t&__wrapper {\n\t\t\t\tflex: 1;\n\t\t\t\t@include flex;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n\t\t\t\theight: 48px;\n\t\t\t\t\n\t\t\t\t&--confirm,\n\t\t\t\t&--only-cancel {\n\t\t\t\t\tborder-bottom-right-radius: $u-modal-border-radius;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t&--cancel,\n\t\t\t\t&--only-confirm {\n\t\t\t\t\tborder-bottom-left-radius: $u-modal-border-radius;\n\t\t\t\t}\n\n\t\t\t\t&--hover {\n\t\t\t\t\tbackground-color: $u-bg-color;\n\t\t\t\t}\n\n\t\t\t\t&__text {\n\t\t\t\t\tcolor: $u-content-color;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-modal/u-modal.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "uni"], "mappings": ";;;;;;AA8HC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,mDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AAAA,IACN,KAAK,GAAG;AAGP,UAAI,KAAK,KAAK;AAAS,aAAK,UAAU;AAAA,IACvC;AAAA,EACA;AAAA,EACD,OAAO,CAAC,WAAW,UAAU,SAAS,eAAe,eAAe;AAAA,EACpE,UAAU;AAAA,IACT,kBAAkB;AACjB,UAAI,QAAQ,KAAK;AACjB,YAAM,aAAa,GAAG,KAAK,QAAQ,KAAK,EAAE;AAC1C,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,SAAAC,0CAAO;AAAA;AAAA,IAEP,iBAAiB;AAEhB,UAAI,KAAK,YAAY;AACpB,aAAK,UAAU;AAAA,aACT;AACN,aAAK,MAAM,eAAe,KAAK;AAAA,MAChC;AACA,WAAK,MAAM,SAAS;AAAA,IACpB;AAAA;AAAA,IAED,gBAAgB;AAEf,UAAI,KAAK,cAAc,KAAK,SAAS;AACpC,YAAI,KAAK,eAAe;AACvBC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AACA,aAAK,MAAM,eAAe;AAAA,aACpB;AAEN,YAAI,CAAC,KAAK,kBAAkB;AAC3B,eAAK,MAAM,eAAe,KAAK;AAAA,QAChC;AAAA,MACD;AACA,WAAK,MAAM,QAAQ;AAAA,IACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMD,eAAe;AACd,UAAI,KAAK,qBAAqB;AAC7B,aAAK,MAAM,eAAe,KAAK;AAC/B,aAAK,MAAM,OAAO;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/LD,GAAG,gBAAgB,SAAS;"}