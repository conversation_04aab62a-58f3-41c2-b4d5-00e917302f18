{"version": 3, "file": "u-safe-bottom.js", "sources": ["uni_modules/uview-plus/components/u-safe-bottom/u-safe-bottom.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9DRENFeGFtQS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1zYWZlLWJvdHRvbS91LXNhZmUtYm90dG9tLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view\r\n\t\tclass=\"u-safe-bottom\"\r\n\t\t:style=\"[style]\"\r\n\t\t:class=\"[!isNvue && 'u-safe-area-inset-bottom']\"\r\n\t>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { props } from \"./props.js\";\r\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\r\n\timport { mixin } from '../../libs/mixin/mixin';\r\n\timport { addStyle, deepMerge, addUnit, getWindowInfo } from '../../libs/function/index';\r\n\t/**\r\n\t * SafeBottom 底部安全区\r\n\t * @description 这个适配，主要是针对IPhone X等一些底部带指示条的机型，指示条的操作区域与页面底部存在重合，容易导致用户误操作，因此我们需要针对这些机型进行底部安全区适配。\r\n\t * @tutorial https://ijry.github.io/uview-plus/components/safeAreaInset.html\r\n\t * @property {type}\t\tprop_name\r\n\t * @property {Object}\tcustomStyle\t定义需要用到的外部样式\r\n\t *\r\n\t * @event {Function()}\r\n\t * @example <u-status-bar></u-status-bar>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-safe-bottom\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsafeAreaBottomHeight: 0,\r\n\t\t\t\tisNvue: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tconst style = {};\r\n\t\t\t\t// #ifdef APP-NVUE || MP-TOUTIAO\r\n\t\t\t\t// nvue下，高度使用js计算填充\r\n\t\t\t\tstyle.height = addUnit(getWindowInfo().safeAreaInsets.bottom, 'px');\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle));\r\n\t\t\t},\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-NVUE\r\n\t\t\t// 标识为是否nvue\r\n\t\t\tthis.isNvue = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.u-safe-bottom {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twidth: 100%;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import Component from 'E:/project/CDCExamA/uni_modules/uview-plus/components/u-safe-bottom/u-safe-bottom.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "addStyle"], "mappings": ";;;;;;AAwBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,wDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,sBAAsB;AAAA,MACtB,QAAQ;AAAA;EAET;AAAA,EACD,UAAU;AAAA,IACT,QAAQ;AACP,YAAM,QAAQ,CAAA;AAKd,aAAOC,0CAAAA,UAAU,OAAOC,0CAAAA,SAAS,KAAK,WAAW,CAAC;AAAA,IAClD;AAAA,EACD;AAAA,EACD,UAAU;AAAA,EAKT;;;;;;;;;AC/CH,GAAG,gBAAgB,SAAS;"}