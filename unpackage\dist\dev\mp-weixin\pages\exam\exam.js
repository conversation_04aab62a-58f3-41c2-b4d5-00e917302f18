"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_exam = require("../../src/api/modules/exam.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "exam",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const currentExams = common_vendor.ref([]);
    const recentHistory = common_vendor.ref([]);
    common_vendor.onMounted(() => {
      if (userStore.isApproved) {
        loadExamData();
      }
    });
    async function loadExamData() {
      try {
        const [exams, history] = await Promise.all([
          src_api_modules_exam.getCurrentExams(),
          src_api_modules_exam.getExamHistory(1, 5)
        ]);
        currentExams.value = exams;
        recentHistory.value = history.list || [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/exam/exam.vue:133", "加载考试数据失败:", error);
      }
    }
    function handleExamClick(exam) {
      if (exam.type === "online") {
        if (exam.status === "not_started") {
          common_vendor.index.navigateTo({ url: `/pages/exam/online-exam?id=${exam.id}` });
        }
      } else {
        common_vendor.index.navigateTo({ url: `/pages/exam/offline-exam?id=${exam.id}` });
      }
    }
    function canTakeExam(exam) {
      return ["not_started", "in_progress"].includes(exam.status);
    }
    function getActionText(exam) {
      if (exam.type === "online") {
        return exam.status === "not_started" ? "开始考试" : "继续考试";
      } else {
        return "立即报名";
      }
    }
    function getStatusText(status) {
      const statusMap = {
        not_started: "未开始",
        in_progress: "进行中",
        completed: "已完成",
        passed: "已通过",
        failed: "未通过",
        expired: "已过期"
      };
      return statusMap[status] || status;
    }
    function formatExamTime(startTime, endTime) {
      const start = new Date(startTime);
      const end = new Date(endTime);
      return `${start.toLocaleDateString()} ${start.toLocaleTimeString()} - ${end.toLocaleTimeString()}`;
    }
    function formatTime(timeStr) {
      return new Date(timeStr).toLocaleDateString();
    }
    function goToProfile() {
      common_vendor.index.switchTab({ url: "/pages/profile/profile" });
    }
    function goToHistory() {
      common_vendor.index.navigateTo({ url: "/pages/exam/history" });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !common_vendor.unref(userStore).isApproved
      }, !common_vendor.unref(userStore).isApproved ? {
        b: common_vendor.o(goToProfile)
      } : common_vendor.e({
        c: currentExams.value.length === 0
      }, currentExams.value.length === 0 ? {} : {
        d: common_vendor.f(currentExams.value, (exam, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(exam.name),
            b: common_vendor.t(exam.type === "online" ? "线上" : "线下"),
            c: common_vendor.n(exam.type),
            d: common_vendor.t(formatExamTime(exam.startTime, exam.endTime)),
            e: common_vendor.t(exam.duration),
            f: common_vendor.t(exam.totalQuestions),
            g: common_vendor.t(getStatusText(exam.status)),
            h: common_vendor.n(exam.status),
            i: canTakeExam(exam)
          }, canTakeExam(exam) ? {
            j: common_vendor.t(getActionText(exam)),
            k: common_vendor.n(exam.type)
          } : {}, {
            l: exam.id,
            m: common_vendor.o(($event) => handleExamClick(exam), exam.id)
          });
        })
      }, {
        e: common_vendor.o(goToHistory),
        f: recentHistory.value.length === 0
      }, recentHistory.value.length === 0 ? {} : {
        g: common_vendor.f(recentHistory.value, (record, k0, i0) => {
          return {
            a: common_vendor.t(record.name),
            b: common_vendor.t(formatTime(record.completedTime)),
            c: common_vendor.t(record.score || "--"),
            d: common_vendor.n(record.status),
            e: common_vendor.t(getStatusText(record.status)),
            f: common_vendor.n(record.status),
            g: record.id
          };
        })
      }));
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-970fed46"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/exam/exam.js.map
