<view class="exam-container data-v-970fed46"><view wx:if="{{a}}" class="access-denied data-v-970fed46"><view class="denied-content data-v-970fed46"><text class="denied-icon data-v-970fed46">🚫</text><text class="denied-title data-v-970fed46">未认证，无法考试</text><text class="denied-desc data-v-970fed46">请先完善个人资料并通过机构审核</text><button class="denied-btn data-v-970fed46" bindtap="{{b}}">去认证</button></view></view><view wx:else class="exam-content data-v-970fed46"><view class="exam-section data-v-970fed46"><view class="section-header data-v-970fed46"><text class="section-title data-v-970fed46">📋 本期考试</text></view><view wx:if="{{c}}" class="empty-state data-v-970fed46"><text class="empty-icon data-v-970fed46">📝</text><text class="empty-text data-v-970fed46">暂无待考试项</text></view><view wx:else class="exam-list data-v-970fed46"><view wx:for="{{d}}" wx:for-item="exam" wx:key="l" class="exam-card data-v-970fed46" bindtap="{{exam.m}}"><view class="exam-header data-v-970fed46"><text class="exam-name data-v-970fed46">{{exam.a}}</text><view class="{{['exam-type', 'data-v-970fed46', exam.c]}}">{{exam.b}}</view></view><view class="exam-info data-v-970fed46"><text class="exam-time data-v-970fed46"> 考试时间: {{exam.d}}</text><text class="exam-duration data-v-970fed46">考试时长: {{exam.e}}分钟</text><text class="exam-questions data-v-970fed46">题目数量: {{exam.f}}题</text></view><view class="exam-status data-v-970fed46"><text class="{{['status-text', 'data-v-970fed46', exam.h]}}">{{exam.g}}</text><button wx:if="{{exam.i}}" class="{{['exam-btn', 'data-v-970fed46', exam.k]}}">{{exam.j}}</button></view></view></view></view><view class="history-section data-v-970fed46"><view class="section-header data-v-970fed46" bindtap="{{e}}"><text class="section-title data-v-970fed46">📊 历史考试记录</text><text class="section-more data-v-970fed46">查看全部</text></view><view wx:if="{{f}}" class="empty-state data-v-970fed46"><text class="empty-text data-v-970fed46">暂无考试记录</text></view><view wx:else class="history-list data-v-970fed46"><view wx:for="{{g}}" wx:for-item="record" wx:key="g" class="history-item data-v-970fed46"><view class="history-info data-v-970fed46"><text class="history-name data-v-970fed46">{{record.a}}</text><text class="history-time data-v-970fed46">{{record.b}}</text></view><view class="history-result data-v-970fed46"><text class="{{['history-score', 'data-v-970fed46', record.d]}}">{{record.c}}分 </text><text class="{{['history-status', 'data-v-970fed46', record.f]}}">{{record.e}}</text></view></view></view></view></view></view>